var Ht=Object.defineProperty;var Tt=(r,e,t)=>e in r?Ht(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var p=(r,e,t)=>(Tt(r,typeof e!="symbol"?e+"":e,t),t);import{j as Be,N as x,u as ke,$ as Bt,a as Et,b as At,c as Ot,d as It,e as Ut,f as Ue,g as M,C as Nt,i as h,B as Gt,h as ht,M as zt,V as y,k as $e,Q as ut,F as Vt,O as jt,l as we,m as T,S as Le,n as z,o as mt,p as Wt,G as Kt,q as ee,r as qt,s as Zt,t as Yt,v as Jt,w as Qt,x as Xt,y as en,z as tn,A as nn,D as on,E as rn,H as _,R as sn,W as He,I as an,J as ln,K as cn,P as dn,L as hn,T as un,U as mn,X as pn,Y as fn,Z as gn,_ as Ne,a0 as Ge,a1 as ze,a2 as Ve,a3 as je,a4 as _n,a5 as yn,a6 as wn,a7 as We,a8 as Sn,a9 as Me,aa as bn,ab as vn,ac as Cn,ad as kn,ae as A,af as O,ag as j,ah as W,ai as b,aj as Pe,ak as oe,al as K,am as re,an as Ln,ao as Mn,ap as Pn,aq as Rn,ar as Dn,as as Fn,at as xn,au as $n,av as Hn,aw as Tn,ax as Bn,ay as En,az as pt,aA as An,aB as On,aC as In,aD as Un,aE as Nn}from"./vendor-3623244b.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))n(o);new MutationObserver(o=>{for(const s of o)if(s.type==="childList")for(const i of s.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&n(i)}).observe(document,{childList:!0,subtree:!0});function t(o){const s={};return o.integrity&&(s.integrity=o.integrity),o.referrerPolicy&&(s.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?s.credentials="include":o.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function n(o){if(o.ep)return;o.ep=!0;const s=t(o);fetch(o.href,s)}})();const Gn=Be.Fragment,a=Be.jsx,f=Be.jsxs,zn="_DialogOverlay_1bzhl_6",Vn="_overlayShow_1bzhl_1",jn="_DialogContent_1bzhl_14",Wn="_contentShow_1bzhl_1",Kn="_DialogTitle_1bzhl_34",qn="_DialogDescription_1bzhl_41",Zn="_Button_1bzhl_51",Yn="_violet_1bzhl_63",Jn="_green_1bzhl_74",Qn="_IconButton_1bzhl_85",Xn={DialogOverlay:zn,overlayShow:Vn,DialogContent:jn,contentShow:Wn,DialogTitle:Kn,DialogDescription:qn,Button:Zn,violet:Yn,green:Jn,IconButton:Qn},{DialogOverlay:eo,DialogContent:to,DialogTitle:no,DialogDescription:oo,Button:ro,IconButton:so,green:ao}=Xn,io=x.create(({title:r,description:e,children:t,button:n})=>{const o=ke();return a(Bt,{defaultOpen:!0,open:o.visible,onOpenChange:s=>{s?o.show():o.hide()},children:f(Et,{children:[a(At,{className:eo}),f(Ot,{className:to,children:[a(It,{className:no,children:r}),a(Ut,{className:oo,children:e}),f("div",{children:[" ",t]}),a("div",{style:{display:"flex",marginTop:25,justifyContent:"flex-end"},children:a(Ue,{asChild:!0,children:a("button",{className:M(ro,ao),onClick:()=>{o.resolve("action")},children:n})})}),a(Ue,{asChild:!0,children:a("button",{className:so,children:a(Nt,{})})})]})]})})});async function lo(r){return console.log(r),await x.show(io,r)}const co="Width",ho="Height",uo="Forearm",mo="Hips",po="Thigh",fo="Redo",go="Undo",_o="Edit",yo="Setting",wo="Feedback",So="View",bo="Del",vo="File",Co="Generate",ko="Close",Lo="Update",Mo={Width:co,Height:ho,"Generate Skeleton/Depth/Normal/Canny Map":"Generate Skeleton/Depth/Normal/Canny Map","Duplicate Skeleton":"Duplicate Skeleton","Delete Skeleton":"Delete Skeleton","Move Mode":"Move Mode","Show Edge Map":"Show Edge Map","Camera Near":"Camera Near","Camera Far":"Camera Far","Camera Focal Length":"Camera Focal Length","Downloading Hand Model":"Downloading Hand Model","Updates are available, please confirm!!":"Updates are available, please confirm!!","Select a scene file":"Select a scene file","Oops...":"Oops...","Something went wrong!":"Something went wrong!","If the problem persists, please click here to ask a question.":"If the problem persists, please click here to ask a question.","Save Scene":"Save Scene","Generate Scene URL":"Generate Scene URL","Load Scene":"Load Scene","Restore Last Scene":"Restore Last Scene","Set Background Image":"Set Background Image","Select an image":"Select an image","Body Parameters":"Body Parameters","Shoulder Width":"Shoulder Width","Shoulder To Hip":"Shoulder To Hip","Arm Length":"Arm Length",Forearm:uo,"Upper Arm":"Upper Arm","Hand Size":"Hand Size",Hips:mo,"Leg Length":"Leg Length",Thigh:po,"Lower Leg":"Lower Leg","Nose To Neck":"Nose To Neck","Head Size":"Head Size","Show Preview":"Show Preview","Only Hand":"Only Hand","Foot Size":"Foot Size","Downloading Foot Model":"Downloading Foot Model","Please select a skeleton!!":"Please select a skeleton!!","Downloading Poses Library":"Downloading Poses Library","Set Random Pose":"Set Random Pose",Redo:fo,Undo:go,"Detect From Image":"Detect From Image","Downloading MediaPipe Pose Model":"Downloading MediaPipe Pose Model",Edit:_o,Setting:yo,"Bone Thickness":"Bone Thickness",Feedback:wo,View:So,"Fix View":"Fix View","Restore View":"Restore View","Free Mode":"Free Mode",Del:bo,File:vo,Generate:Co,Close:ko,Update:Lo,"Lock View":"Lock View","Unlock View":"Unlock View","Copy Keypoint Data":"Copy Keypoint Data","Copied to Clipboard":"Copied to Clipboard","Reset Scene":"Reset Scene","Load Gesture":"Load Gesture","Save Gesture":"Save Gesture","Please select a hand!!":"Please select a hand!!","If you try to detect anime characters, you may get an error. Please try again with photos.":"If you try to detect anime characters, you may get an error. Please try again with photos.","Show Grid":"Show Grid"},Po="宽度",Ro="高度",Do="前臂",Fo="臀部宽度",xo="大腿",$o="重做",Ho="撤销",To="编辑",Bo="设置",Eo="反馈",Ao="视图",Oo="Del",Io="文件",Uo="生成",No="关闭",Go="更新",zo={Width:Po,Height:Ro,"Generate Skeleton/Depth/Normal/Canny Map":"生成骨架/深度/法线/Canny图","Duplicate Skeleton":"复制骨架","Delete Skeleton":"删除骨架","Move Mode":"移动模式","Show Edge Map":"显示边缘图","Camera Near":"相机near","Camera Far":"相机far","Camera Focal Length":"相机焦距","Downloading Hand Model":"正在下载手部模型","Updates are available, please confirm!!":"有更新啦，请确认！！","Select a scene file":"选择一个场景文件","Oops...":"哎呀...","Something went wrong!":"出错了！","If the problem persists, please click here to ask a question.":"如果问题一直存在，请点击此处提问。","Save Scene":"保存场景","Load Scene":"加载场景","Restore Last Scene":"恢复上次场景","Set Background Image":"设置背景图片","Select an image":"选择一张图片","Body Parameters":"身体参数","Shoulder Width":"肩宽","Shoulder To Hip":"肩到臀","Arm Length":"手臂长度",Forearm:Do,"Upper Arm":"上臂","Hand Size":"手部尺寸",Hips:Fo,"Leg Length":"腿部长度",Thigh:xo,"Lower Leg":"小腿","Nose To Neck":"鼻子到颈部","Head Size":"头部大小","Show Preview":"显示预览图","Only Hand":"仅显示手","Foot Size":"脚部尺寸","Downloading Foot Model":"正在下载脚部模型","Please select a skeleton!!":"请选择骨架！","Downloading Poses Library":"正在下载姿势库","Set Random Pose":"设置随机姿势",Redo:$o,Undo:Ho,"Detect From Image":"从图片中检测","Downloading MediaPipe Pose Model":"正在下载 MediaPipe 姿势模型",Edit:To,Setting:Bo,"Bone Thickness":"骨骼厚度",Feedback:Eo,View:Ao,"Fix View":"固定视图","Restore View":"恢复视图","Free Mode":"自由模式",Del:Oo,File:Io,Generate:Uo,Close:No,Update:Go,"Lock View":"锁定视图","Unlock View":"解除锁定视图","Copy Keypoint Data":"复制关键点数据","Copied to Clipboard":"已复制到剪贴板","Generate Scene URL":"生成场景URL","Reset Scene":"重置场景","Load Gesture":"加载手势","Save Gesture":"保存手势","Please select a hand!!":"请选择一只手！！","If you try to detect anime characters, you may get an error. Please try again with photos.":"如果您尝试检测动漫角色，可能会出现错误。请使用照片再试一次。","Show Grid":"显示网格"},Vo="寬度",jo="高度",Wo="前臂",Ko="臀部",qo="大腿",Zo="重做",Yo="撤銷",Jo="編輯",Qo="設定",Xo="回饋",er="檢視",tr="Del",nr="檔案",or="生成",rr="關閉",sr="更新",ar={Width:Vo,Height:jo,"Generate Skeleton/Depth/Normal/Canny Map":"產生骨架/深度/法線/Canny圖","Duplicate Skeleton":"複製骨架","Delete Skeleton":"刪除骨架","Move Mode":"移動模式","Show Edge Map":"顯示邊緣圖","Camera Near":"相機近端距離","Camera Far":"相機遠端距離","Camera Focal Length":"相機焦距","Downloading Hand Model":"下載手部模型中","Updates are available, please confirm!!":"有更新，請確認！！","Select a scene file":"選擇場景檔案","Oops...":"糟糕...","Something went wrong!":"出了些問題！","If the problem persists, please click here to ask a question.":"如果問題持續存在，請點擊此處發起提問。","Save Scene":"儲存場景","Load Scene":"載入場景","Restore Last Scene":"恢復上一個場景","Set Background Image":"設定背景圖片","Select an image":"選擇圖片","Body Parameters":"身體參數","Shoulder Width":"肩寬","Shoulder To Hip":"肩臀長","Arm Length":"手臂長",Forearm:Wo,"Upper Arm":"上臂","Hand Size":"手部大小",Hips:Ko,"Leg Length":"腿長",Thigh:qo,"Lower Leg":"小腿","Nose To Neck":"鼻子到頸部的距離","Head Size":"頭部大小","Show Preview":"顯示預覽","Only Hand":"僅手部","Foot Size":"腳部大小","Downloading Foot Model":"下載腳部模型中","Please select a skeleton!!":"請選擇骨架！","Downloading Poses Library":"正在下載姿勢庫","Set Random Pose":"設置隨機姿勢",Redo:Zo,Undo:Yo,"Detect From Image":"從圖片中偵測","Downloading MediaPipe Pose Model":"下載 MediaPipe 姿勢模型中",Edit:Jo,Setting:Qo,"Bone Thickness":"骨骼厚度",Feedback:Xo,View:er,"Fix View":"固定檢視","Restore View":"還原檢視","Free Mode":"自由模式",Del:tr,File:nr,Generate:or,Close:rr,Update:sr,"Lock View":"鎖定視圖","Unlock View":"解鎖視圖","Copy Keypoint Data":"複製關鍵點數據","Copied to Clipboard":"已複製到剪貼板","Generate Scene URL":"生成場景URL","Reset Scene":"重置場景","Load Gesture":"載入手勢","Save Gesture":"儲存手勢","Please select a hand!!":"請選擇一隻手！！","If you try to detect anime characters, you may get an error. Please try again with photos.":"如果您嘗試檢測動漫角色，可能會出現錯誤。請使用照片再試一次。","Show Grid":"顯示網格"},ir="寬度",lr="高度",cr="前臂",dr="臀部",hr="大腿",ur="重做",mr="撤銷",pr="編輯",fr="設定",gr="回饋",_r="檢視",yr="Del",wr="文件",Sr="生成",br="關閉",vr="更新",Cr={Width:ir,Height:lr,"Generate Skeleton/Depth/Normal/Canny Map":"生成骨架/深度/法線/Canny圖","Duplicate Skeleton":"複製骨架","Delete Skeleton":"刪除骨架","Move Mode":"移動模式","Show Edge Map":"顯示邊緣圖","Camera Near":"相機near","Camera Far":"相機far","Camera Focal Length":"相機焦距","Downloading Hand Model":"正在下載手部模型","Updates are available, please confirm!!":"有更新啦，请确认！！","Select a scene file":"選擇場景檔案","Oops...":"哎呀...","Something went wrong!":"出現問題！","If the problem persists, please click here to ask a question.":"如果問題仍然存在，請點擊此處發問。","Save Scene":"儲存場景","Load Scene":"載入場景","Restore Last Scene":"恢復上次場景","Set Background Image":"設置背景圖片","Select an image":"選擇圖片","Body Parameters":"身體參數","Shoulder Width":"肩寬","Shoulder To Hip":"肩臀長度","Arm Length":"手臂長度",Forearm:cr,"Upper Arm":"上臂","Hand Size":"手部尺寸",Hips:dr,"Leg Length":"腿長",Thigh:hr,"Lower Leg":"小腿","Nose To Neck":"鼻子到脖子的距離","Head Size":"頭部尺寸","Show Preview":"顯示預覽","Only Hand":"只顯示手部","Foot Size":"腳部尺寸","Downloading Foot Model":"正在下載腳部模型","Please select a skeleton!!":"請選擇骨架！","Downloading Poses Library":"下載姿勢庫中","Set Random Pose":"設定隨機姿勢",Redo:ur,Undo:mr,"Detect From Image":"從圖片中偵測","Downloading MediaPipe Pose Model":"下載 MediaPipe 姿勢模型",Edit:pr,Setting:fr,"Bone Thickness":"骨骼厚度",Feedback:gr,View:_r,"Fix View":"固定檢視","Restore View":"還原檢視","Free Mode":"自由模式",Del:yr,File:wr,Generate:Sr,Close:br,Update:vr,"Lock View":"鎖定視圖","Unlock View":"解鎖視圖","Copy Keypoint Data":"複製關鍵點數據","Copied to Clipboard":"已複製到剪貼板","Generate Scene URL":"生成場景URL","Reset Scene":"重置場景","Load Gesture":"載入手勢","Save Gesture":"儲存手勢","Please select a hand!!":"請選擇一隻手！！","If you try to detect anime characters, you may get an error. Please try again with photos.":"如果您嘗試檢測動漫角色，可能會出現錯誤。請使用照片再試一次。","Show Grid":"顯示網格"},kr="幅",Lr="高さ",Mr="前腕",Pr="ヒップ",Rr="太もも",Dr="やり直し",Fr="元に戻す",xr="編集",$r="設定",Hr="フィードバック",Tr="ビュー",Br="Del",Er="ファイル",Ar="生成する",Or="閉じる",Ir="更新する",Ur={Width:kr,Height:Lr,"Generate Skeleton/Depth/Normal/Canny Map":"スケルトン/深度/法線/Cannyマップを生成","Duplicate Skeleton":"スケルトンの複製","Delete Skeleton":"スケルトンの削除","Move Mode":"移動モード","Show Edge Map":"エッジマップを表示","Camera Near":"カメラ近距離","Camera Far":"カメラ遠距離","Camera Focal Length":"カメラ焦点距離","Downloading Hand Model":"手のモデルをダウンロード中","Updates are available, please confirm!!":"アップデートが利用可能です。確認してください！","Select a scene file":"シーンファイルを選択","Oops...":"おっと...","Something went wrong!":"何かが間違っています！","If the problem persists, please click here to ask a question.":"問題が解決しない場合は、ここをクリックして質問してください。","Save Scene":"シーンを保存","Generate Scene URL":"シーンのURLを生成","Load Scene":"シーンを読み込む","Restore Last Scene":"前回のシーンを復元","Set Background Image":"背景画像を設定","Select an image":"画像を選択","Body Parameters":"身体パラメータ","Shoulder Width":"肩幅","Shoulder To Hip":"肩から腰までの距離","Arm Length":"腕の長さ",Forearm:Mr,"Upper Arm":"上腕","Hand Size":"手の大きさ",Hips:Pr,"Leg Length":"脚の長さ",Thigh:Rr,"Lower Leg":"下腿","Nose To Neck":"鼻から首までの距離","Head Size":"頭の大きさ","Show Preview":"プレビューを表示","Only Hand":"手だけ","Foot Size":"足のサイズ","Downloading Foot Model":"足のモデルをダウンロード中","Please select a skeleton!!":"骨格を選択してください！","Downloading Poses Library":"ポーズライブラリをダウンロード中","Set Random Pose":"ランダムポーズを設定",Redo:Dr,Undo:Fr,"Detect From Image":"画像から検出","Downloading MediaPipe Pose Model":"MediaPipeポーズモデルをダウンロード中",Edit:xr,Setting:$r,"Bone Thickness":"ボーンの太さ",Feedback:Hr,View:Tr,"Fix View":"ビューを固定する","Restore View":"ビューを復元する","Free Mode":"フリーモード",Del:Br,File:Er,Generate:Ar,Close:Or,Update:Ir,"Lock View":"ビューをロックする","Unlock View":"ビューをアンロックする","Copy Keypoint Data":"キーポイントデータをコピーする","Copied to Clipboard":"クリップボードにコピーされました","Reset Scene":"シーンをリセットする","Load Gesture":"ジェスチャーを読み込む","Save Gesture":"ジェスチャーを保存する","Please select a hand!!":"手を選択してください!!","If you try to detect anime characters, you may get an error. Please try again with photos.":"アニメキャラクターを検出しようとするとエラーが発生する場合があります。写真で再度お試しください。","Show Grid":"グリッドを表示"},Nr="Breite",Gr="Höhe",zr="Unterarm",Vr="Gesäß",jr="Oberschenkel",Wr="Redo",Kr="Undo",qr="bearbeiten",Zr="Einstellungen",Yr="Problem melden",Jr="Ansicht",Qr="Entf",Xr="Datei",es="Generieren",ts="Schließen",ns="Aktualisieren",os={Width:Nr,Height:Gr,"Generate Skeleton/Depth/Normal/Canny Map":"Knochenbau/Depth/Normale/Canny Bilder generieren","Duplicate Skeleton":"Knochenbau kopieren","Delete Skeleton":"Knochenbau löschen","Move Mode":"Verschiebungsmode","Show Edge Map":"Randbild zeigen","Camera Near":"Kamera näher schieben","Camera Far":"Kamera weiter ziehen","Camera Focal Length":"Brennweite","Downloading Hand Model":"Hand Modell herunterladen","Updates are available, please confirm!!":"Bestätigen Sie die Aktualisierung!","Select a scene file":"Wählen Sie eine Szene","Oops...":"Oops...","Something went wrong!":"Irgendwas hat schief gelaufen!","If the problem persists, please click here to ask a question.":"Bitte melden Sie hier, um Frage zu stellen.","Save Scene":"Szene speichern","Load Scene":"Szene laden","Restore Last Scene":"Szene wiederherstellen","Set Background Image":"Hintergrundbild laden","Select an image":"Wählen Sie ein Bild","Body Parameters":"Körper Parameter","Shoulder Width":"Schulterbreite","Shoulder To Hip":"Von Schulter zu Gesäß","Arm Length":"Armlänge",Forearm:zr,"Upper Arm":"Oberarm","Hand Size":"Handgröße",Hips:Vr,"Leg Length":"Beinlänge",Thigh:jr,"Lower Leg":"Unterschenkel","Nose To Neck":"Von Nase zu Hals","Head Size":"Kopfgröße","Show Preview":"Überblick zeigen","Only Hand":"Nur Hände","Foot Size":"Fußgröße","Downloading Foot Model":"Fuß Modell herunterladen","Please select a skeleton!!":"Wählen Sie einen Knochenbau!","Downloading Poses Library":"Pose Bibliothek herunterladen","Set Random Pose":"zufällige Pose",Redo:Wr,Undo:Kr,"Detect From Image":"Knochenbau von Bild erkennen","Downloading MediaPipe Pose Model":"MediaPipe Pose Modell herunterladen",Edit:qr,Setting:Zr,"Bone Thickness":"Dicke des Knochens",Feedback:Yr,View:Jr,"Fix View":"Ansicht fixieren","Restore View":"Ansicht wiederherstellen","Free Mode":"Freier Modus",Del:Qr,File:Xr,Generate:es,Close:ts,Update:ns,"Lock View":"Ansicht sperren","Unlock View":"Ansicht entsperren","Copy Keypoint Data":"Schlüsselpunktdaten kopieren","Copied to Clipboard":"In die Zwischenablage kopiert","Generate Scene URL":"Szene-URL generieren","Reset Scene":"Szene zurücksetzen","Load Gesture":"Geste laden","Save Gesture":"Geste speichern","Please select a hand!!":"Bitte wählen Sie eine Hand aus!!","If you try to detect anime characters, you may get an error. Please try again with photos.":"Bei dem Versuch, Anime-Charaktere zu erkennen, kann es zu einem Fehler kommen. Bitte versuchen Sie es erneut mit Fotos.","Show Grid":"Gitter anzeigen"},rs="ancho",ss="altura",as="antebrazo",is="caderas",ls="muslo",cs="rehacer",ds="deshacer",hs="editar",us="ajustes",ms="realimentación",ps="Ver",fs="Remoto",gs="Archivo",_s="Generar",ys="Cerrar",ws="Actualizar",Ss={Width:rs,Height:ss,"Generate Skeleton/Depth/Normal/Canny Map":"generar esqueleto/profundidad/normal/generar mapa astuto","Duplicate Skeleton":"copiar esqueleto","Delete Skeleton":"eliminar esqueleto","Move Mode":"modo de movimiento","Show Edge Map":"mostart mapa de borde","Camera Near":"cámera cerca","Camera Far":"cámera lejos","Camera Focal Length":"distancia focal de la cámara","Downloading Hand Model":"descargar el modelo de mano","Updates are available, please confirm!!":"confirmar la(s) actualización(es)","Select a scene file":"selecciona un archivo de escena","Oops...":"¡Uy!","Something went wrong!":"algo salío mal","If the problem persists, please click here to ask a question.":"si el problema persiste haga clic aquí oara hacer una pregunta.","Save Scene":"guardar escena","Load Scene":"cargar la escena","Restore Last Scene":"restaurar la última escena","Set Background Image":"establecer la imagen de fondo","Select an image":"selecciona una imagen","Body Parameters":"parámetros del cuepro","Shoulder Width":"ancho de los hombros","Shoulder To Hip":"de hombro a cadera","Arm Length":"longitud del brazo",Forearm:as,"Upper Arm":"brazo ","Hand Size":"tamaño de la mano",Hips:is,"Leg Length":"longitud de la pierna",Thigh:ls,"Lower Leg":"parte inferior de la pierna","Nose To Neck":"de la nariz al cuello","Head Size":"tamaño de la cabeza","Show Preview":"mostrar vista previa","Only Hand":"solo manos","Foot Size":"tamaño del pie","Downloading Foot Model":"descargando el modelo de pie","Please select a skeleton!!":"¡selecciona un esqueleto!","Downloading Poses Library":"descargando la biblioteca de poses","Set Random Pose":"pose aletoria",Redo:cs,Undo:ds,"Detect From Image":"detectar desde la imagen","Downloading MediaPipe Pose Model":"descargando el modelo de pose de MediaPipe",Edit:hs,Setting:us,"Bone Thickness":"grosor del hueso",Feedback:ms,View:ps,"Fix View":"Fijar Vista","Restore View":"Restaurar Vista","Free Mode":"Modo libre",Del:fs,File:gs,Generate:_s,Close:ys,Update:ws,"Lock View":"Bloquear vista","Unlock View":"Desbloquear vista","Copy Keypoint Data":"Copiar datos del punto clave","Copied to Clipboard":"Copiado al portapapeles","Generate Scene URL":"Generar URL de escena","Reset Scene":"Restablecer escena","Load Gesture":"Cargar gesto","Save Gesture":"Guardar gesto","Please select a hand!!":"¡¡Por favor, seleccione una mano!!","If you try to detect anime characters, you may get an error. Please try again with photos.":"Si intenta detectar personajes de anime, es posible que obtenga un error. Por favor, inténtelo de nuevo con fotos.","Show Grid":"Mostrar cuadrícula"},Ke={en:{common:Mo},zh:{common:zo},"zh-TW":{common:ar},"zh-HK":{common:Cr},ja:{common:Ur},de:{common:os},sp:{common:Ss}},Re={en:"English",zh:"简体中文","zh-TW":"繁體中文（台灣）","zh-HK":"繁體中文（香港）",ja:"日本語",de:"Deutsch",sp:"español"},bs={order:["querystring","localStorage","navigator"],lookupQuerystring:"lng"};h.use(Gt).init({detection:bs,fallbackLng:"en",debug:!0,ns:["common"],defaultNS:"common",supportedLngs:Object.keys(Ke),interpolation:{escapeValue:!1},resources:Ke});function Ee(){var r;return h.language==="zh-CN"||window.navigator.language==="zh-CN"||((r=window.navigator.language)==null?void 0:r.includes("zh-CN"))}console.log("0.1.23  "+ht(1688653454591).format("YYYY-MM-DD HH:mm:ss"));function ft(r,e){const t=document.createElement("a");t.setAttribute("href",r),t.setAttribute("download",e),t.click()}function qe(r,e){const t=new Blob([r],{type:"text/json"}),n=window.URL.createObjectURL(t);ft(n,e),URL.revokeObjectURL(n)}async function gt(r={}){let e;return Array.isArray(r)?e=r:e=[r],new Promise((t,n)=>{const o=document.createElement("input");o.type="file";const s=[...e.map(u=>u.mimeTypes||[]),...e.map(u=>u.extensions||[])].join();o.multiple=e[0].multiple||!1,o.accept=s||"",o.style.display="none",document.body.append(o);const i=()=>c==null?void 0:c(n),d=u=>{typeof c=="function"&&c(),t(u)},c=e[0].legacySetup&&e[0].legacySetup(d,i,o),l=()=>{window.removeEventListener("focus",l),o.remove()};o.addEventListener("click",()=>{window.addEventListener("focus",l)}),o.addEventListener("change",()=>{window.removeEventListener("focus",l),o.remove(),o.files?d(o.multiple?Array.from(o.files):o.files[0]):i()}),"showPicker"in HTMLInputElement.prototype?o.showPicker():o.click()})}async function _t(){try{const r=await gt({mimeTypes:["application/json"],legacySetup:(e,t)=>{const n=setTimeout(t,1e4);return o=>{clearTimeout(n),o&&(console.error("reject"),o("Failed to Open file"))}}});return await new Promise((e,t)=>{const n=new FileReader;n.onload=function(){e(n.result)},n.onerror=function(){t(n.error)},Array.isArray(r)==!1?n.readAsText(r):t("Don't select multiple files")})}catch(r){return console.log(r),null}}async function yt(){try{const r=await gt({mimeTypes:["image/*"],legacySetup:(e,t)=>{const n=setTimeout(t,1e4);return o=>{clearTimeout(n),console.log("reject"),o&&o("Open file timeout")}}});return await new Promise((e,t)=>{const n=new FileReader;n.onload=function(){e(n.result)},n.onerror=function(){t(n.error)},Array.isArray(r)==!1?n.readAsDataURL(r):t("Don't select multiple files")})}catch(r){return console.log(r),null}}async function Ze(r){try{await navigator.clipboard.writeText(r)}catch{const t=document.createElement("input");t.type="text",t.style.display="none",t.value=r,t.ariaHidden="true",t.style.all="unset",t.style.position="fixed",t.style.top="0",t.style.clip="rect(0, 0, 0, 0)",document.body.append(t),t.select();const n=document.execCommand("copy");if(t.remove(),!n)throw new Error("copy command was unsuccessful")}}const vs="_app_1w6pk_1",Cs="_threejsCanvas_1w6pk_9",ks="_gallery_1w6pk_15",Ls="_background_1w6pk_35",Ms={app:vs,threejsCanvas:Cs,gallery:ks,background:Ls},Ps="_MenubarRoot_1qs9g_5",Rs="_MenubarTrigger_1qs9g_14",Ds="_Blue_1qs9g_35",Fs="_MenubarContent_1qs9g_40",xs="_MenubarSubContent_1qs9g_41",$s="_MenubarItem_1qs9g_53",Hs="_MenubarSubTrigger_1qs9g_54",Ts="_MenubarCheckboxItem_1qs9g_55",Bs="_MenubarRadioItem_1qs9g_56",Es="_inset_1qs9g_71",As="_MenubarItemIndicator_1qs9g_104",Os="_MenubarSeparator_1qs9g_113",Is="_RightSlot_1qs9g_119",Us={MenubarRoot:Ps,MenubarTrigger:Rs,Blue:Ds,MenubarContent:Fs,MenubarSubContent:xs,MenubarItem:$s,MenubarSubTrigger:Hs,MenubarCheckboxItem:Ts,MenubarRadioItem:Bs,inset:Es,MenubarItemIndicator:As,MenubarSeparator:Os,RightSlot:Is};function Ns(r){return new Promise((e,t)=>{const n=document.createElement("img");n.src=r,n.addEventListener("load",()=>{e(n)}),n.addEventListener("abort",()=>{t("onabort")}),n.addEventListener("error",()=>{t("onerror")})})}const te={"models/hand.fbx":"../models/hand.fbx","models/foot.fbx":"../models/foot.fbx","src/poses/data.bin":"../src/poses/data.bin"};async function Gs(){try{const e=new URLSearchParams(window.location.search).get("config");if(e!=null&&e.startsWith("/")){const n=await(await fetch(e)).json();Object.assign(te,n.assets)}}catch(r){console.error(r)}}Gs();console.log("@mediapipe/pose",zt);const wt=window.Pose;console.log("MyPose",wt);const zs="https://openpose-editor.oss-cn-beijing.aliyuncs.com/%40mediapipe/pose",Vs="https://cdn.jsdelivr.net/npm/@mediapipe/pose";let St=!0;function js(){return St?Vs:zs}function bt(r){St=r}const ye=new wt({locateFile:r=>{if(r in te)return console.log("local",r),te[r];const e=`${js()}/${r}`;return console.log("load pose model",e),e}});ye.setOptions({modelComplexity:1,smoothLandmarks:!0,enableSegmentation:!0,smoothSegmentation:!0,minDetectionConfidence:.5,minTrackingConfidence:.5});function Ws(r){return new Promise((e,t)=>{let n=!1;const o=setTimeout(()=>{n=!0,t("Timeout")},60*1e3);ye.reset(),ye.send({image:r}),ye.onResults(s=>{console.log(s),n||(clearTimeout(o),e(s))})})}function Ae(r,e){let t=null;return r.traverse(n=>{n.name==e&&(t=n)}),t}function vt(r){const e=new y;return r.getWorldPosition(e),e}function Ks(r,e){return r.worldToLocal(e.clone())}const Ye=new ut,Je=new y,se=new y,Qe=new y,ae=new y,De=new y,ce=new ut,qs=new y,Fe=new y,Xe=new y;new $e;class Zs{constructor(e=[]){p(this,"iks");this.iks=e,this._valid()}update(){const e=this.iks;for(let t=0,n=e.length;t<n;t++)this.updateOne(e[t]);return this}updateOne(e){const t=e.links.map(c=>c.index),n=Math,o=e.effector,s=e.target;Je.setFromMatrixPosition(s.matrixWorld);const i=e.links,d=e.iteration!==void 0?e.iteration:1;for(let c=0;c<d;c++){let l=!1;for(let u=0,S=i.length;u<S;u++){const m=t[u];if(i[u].enabled===!1)break;const g=i[u].limitation,w=i[u].rotationMin,V=i[u].rotationMax;m.matrixWorld.decompose(De,ce,qs),ce.invert(),Qe.setFromMatrixPosition(o.matrixWorld),ae.subVectors(Qe,De),ae.applyQuaternion(ce),ae.normalize(),se.subVectors(Je,De),se.applyQuaternion(ce),se.normalize();let C=se.dot(ae);if(C>1?C=1:C<-1&&(C=-1),C=n.acos(C),!(C<1e-5)){if(e.minAngle!==void 0&&C<e.minAngle&&(C=e.minAngle),e.maxAngle!==void 0&&C>e.maxAngle&&(C=e.maxAngle),Fe.crossVectors(ae,se),Fe.normalize(),Ye.setFromAxisAngle(Fe,C),m.quaternion.multiply(Ye),g!==void 0){let E=m.quaternion.w;E>1&&(E=1);const ne=n.sqrt(1-E*E);m.quaternion.set(g.x*ne,g.y*ne,g.z*ne,E)}w!==void 0&&m.rotation.setFromVector3(Xe.setFromEuler(m.rotation).max(w)),V!==void 0&&m.rotation.setFromVector3(Xe.setFromEuler(m.rotation).min(V)),m.updateMatrixWorld(!0),l=!0}}if(!l)break}return this}_valid(){const e=this.iks;for(let t=0,n=e.length;t<n;t++){const o=e[t],s=o.effector,i=o.links;let d,c;d=s;for(let l=0,u=i.length;l<u;l++)c=i[l].index,d.parent!==c&&console.warn("THREE.CCDIKSolver: bone "+d.name+" is not the child of bone "+c.name),d=c}}}const Ct=["nose","neck","right_shoulder","right_elbow","right_wrist","left_shoulder","left_elbow","left_wrist","right_hip","right_knee","right_ankle","left_hip","left_knee","left_ankle","right_eye","left_eye","right_ear","left_ear"],B=Ct,Ys=[[1,2],[1,5],[2,3],[3,4],[5,6],[6,7],[1,8],[8,9],[9,10],[1,11],[11,12],[12,13],[0,1],[0,14],[14,16],[0,15],[15,17]],kt=[[255,0,0],[255,85,0],[255,170,0],[255,255,0],[170,255,0],[85,255,0],[0,255,0],[0,255,85],[0,255,170],[0,255,255],[0,170,255],[0,85,255],[0,0,255],[85,0,255],[170,0,255],[255,0,255],[255,0,170],[255,0,85]];function Js([r,e,t]){return(r<<16)+(e<<8)+t}function et(r,e){const t=Ys.findIndex(([n,o])=>n===r&&o===e);if(typeof t<"u"){const[n,o,s]=kt[t];return(n<<16)+(o<<8)+s}return null}function Qs(r,e){if(!r||!e)return null;const t=B.indexOf(r),n=B.indexOf(e);return t===-1||n===-1?null:t>n?et(n,t):et(t,n)}const P=1,Lt={Root:0,Hips:1,Spine:2,Spine1:3,Spine2:4,Chest:5,Neck:6,Head:7,Eye_R:8,Eye_L:9,Head_Null:10,Shoulder_L:11,Arm_L:12,ForeArm_L:13,Hand_L:14,HandPinky1_L:15,HandPinky2_L:16,HandPinky3_L:17,HandRing1_L:18,HandRing2_L:19,HandRing3_L:20,HandMiddle1_L:21,HandMiddle2_L:22,HandMiddle3_L:23,HandIndex1_L:24,HandIndex2_L:25,HandIndex3_L:26,HandThumb1_L:27,HandThumb2_L:28,HandThumb3_L:29,Elbow_L:30,ForeArmTwist_L:31,ArmTwist_L:32,Shoulder_R:33,Arm_R:34,ForeArm_R:35,Hand_R:36,HandPinky1_R:37,HandPinky2_R:38,HandPinky3_R:39,HandRing1_R:40,HandRing2_R:41,HandRing3_R:42,HandMiddle1_R:43,HandMiddle2_R:44,HandMiddle3_R:45,HandIndex1_R:46,HandIndex2_R:47,HandIndex3_R:48,HandThumb1_R:49,HandThumb2_R:50,HandThumb3_R:51,Elbow_R:52,ForeArmTwist_R:53,ArmTwist_R:54,UpLeg_L:55,Leg_L:56,Knee_L:57,Foot_L:58,FootPinky1_L:59,FootRing_L:60,FootMiddle_L:61,FootIndex_L:62,FootThumb_L:63,UpLegTwist_L:64,ThighFront_L:65,UpLeg_R:66,Leg_R:67,Knee_R:68,Foot_R:69,FootPinky1_R:70,FootRing_R:71,FootMiddle_R:72,FootIndex_R:73,FootThumb_R:74,UpLegTwist_R:75,ThighFront_R:76},Xs={nose:0,left_eye_inner:1,left_eye:2,left_eye_outer:3,right_eye_inner:4,right_eye:5,right_eye_outer:6,left_ear:7,right_ear:8,mouth_left:9,mouth_right:10,left_shoulder:11,right_shoulder:12,left_elbow:13,right_elbow:14,left_wrist:15,right_wrist:16,left_pinky:17,right_pinky:18,left_index:19,right_index:20,left_thumb:21,right_thumb:22,left_hip:23,right_hip:24,left_knee:25,right_knee:26,left_ankle:27,right_ankle:28,left_heel:29,right_heel:30,left_foot_index:31,right_foot_index:32},ea=new Vt;async function Mt(r,e){return new Promise((t,n)=>{ea.load(r,function(o){t(o)},function(o){console.log(o.loaded/o.total*100+"% loaded"),e==null||e(o.loaded/o.total*100)},function(o){console.log("An error happened"),n(o)})})}new jt;let Y=null,J=null;const Se={meshName:"shoupolySurface1",bonePrefix:"shoujoint"},be={meshName:"FootObject",bonePrefix:"FootBone"},tt={left_hand:Se,right_hand:Se,left_foot:be,right_foot:be};function ta(r){return!!(r.startsWith(Se.bonePrefix)||r.startsWith(be.bonePrefix))}async function na(r,e){const t=await Mt(r,e),n=Ae(t,Se.meshName);n.material=new we,n.skeleton.bones.forEach(s=>{const i=new T(new Le(.2),new z({color:16711680,depthTest:!1,opacity:1,transparent:!0}));i.name="red_point",s.add(i)});const o=new T(new mt(1,1,.4,32),new z({color:0}));return o.name="hand_mask",o.visible=!1,o.rotateZ(Math.PI/2),n.skeleton.bones[0].add(o),Y=t,t}async function oa(r,e){const t=await Mt(r,e);console.log(t);const n=Ae(t,be.meshName);n.material=new we,console.log(n.skeleton.bones),n.skeleton.bones.forEach(s=>{if(s.name!=="FootBone2")return;const i=new T(new Le(.1),new z({color:16711680,depthTest:!1,opacity:1,transparent:!0}));i.name="red_point",i.translateX(-.3),s.add(i)});const o=new T(new mt(.35,.35,.2,32),new z({color:0}));return o.scale.setX(.7),o.name="foot_mask",o.visible=!1,n.skeleton.bones[0].add(o),J=t,t}function ra(r){const e=B.indexOf(r);return e!==-1?Js(kt[e]):0}function sa(r,e,t){const n=Qs(e,t),o=new z({color:n??0,opacity:.6,transparent:!0}),s=new T(new Le(P),o);return s.name=e+"_link_"+t,r.add(s),s}function nt(r,e=P){var n;const t=r.name+"_joint_sphere";(n=r.getObjectByName(t))==null||n.scale.setScalar(e)}function Pt(r,e,t,n,o=P,s=!1){const i=new y(0,0,0),d=e.position,c=i.distanceTo(d),l=i.clone().add(d).multiplyScalar(.5),u=d.clone().sub(i),S=new y(1,0,0),m=S.clone().cross(u),g=S.clone().angleTo(u),w=s?sa(r,t,n):r.getObjectByName(t+"_link_"+n);w.scale.copy(new y(c/2,o/P,o/P)),w.position.copy(l),w.setRotationFromAxisAngle(m.normalize(),g),nt(r,o),nt(e,o)}function aa(r,e,t=P,n=!1){Pt(r,e,r.name,e.name,t,n)}function Rt(r,e,t){const n=new T(new Le(e),new z({color:t}));return n.name=r,n}function U(r,e=0,t=0,n=0){const o=new Kt;return o.name=r,o.translateX(e),o.translateY(t),o.translateZ(n),o}function k(r,e=0,t=0,n=0,o=P){return U(r,e,t,n).add(Rt(r+"_joint_sphere",o,ra(r)))}let Q=null;function de(){return Q?ee(Q):null}const N=2.2,G=.25;function ot(r){if(!Y||!J)throw new Error("Failed to create body");if(r==="right_hand"){const e=ee(Y);return e.name="right_hand",e.translateX(-.4),e.translateY(3),e.rotateY(Math.PI),e.rotateZ(-Math.PI/2),e.scale.multiplyScalar(N),e.traverse(t=>{F(t.name)&&(t.name=t.name+"_R")}),e}else{const e=ee(Y);return e.name="left_hand",e.scale.x=-1,e.translateX(.4),e.translateY(3),e.rotateY(Math.PI),e.rotateZ(Math.PI/2),e.scale.multiplyScalar(N),e.traverse(t=>{F(t.name)&&(t.name=t.name+"_L")}),e}}function rt(r){if(!Y||!J)throw new Error("Failed to create body");if(r==="right_foot"){const e=ee(J);return e.name="right_foot",e.scale.setX(-1),e.scale.multiplyScalar(G),e.traverse(t=>{F(t.name)&&(t.name=t.name+"_R")}),e}else{const e=ee(J);return e.name="left_foot",e.scale.multiplyScalar(G),e.traverse(t=>{F(t.name)&&(t.name=t.name+"_L")}),e}}function ia(){if(!Y||!J)throw new Error("Failed to create body");const r=34,e=46,t=U("torso",0,115,0).add(Rt("center",P,8947848),U("five",0,e/2,0).add(k("neck").add(k("nose",0,20,14).add(k("right_eye",-3,3,-3).add(k("right_ear",-4,-3,-8)),k("left_eye",3,3,-3).add(k("left_ear",4,-3,-8)))),U("right_shoulder_inner").add(k("right_shoulder",-r/2,0,0).add(k("right_elbow",0,-25,0).add(k("right_wrist",0,-25,0).add(ot("right_hand"))))),U("left_shoulder_inner").add(k("left_shoulder",r/2,0,0).add(k("left_elbow",0,-25,0).add(k("left_wrist",0,-25,0).add(ot("left_hand"))))),U("right_hip_inner").add(k("right_hip",-r/2+7,-e,0).add(k("right_knee",0,-40,0).add(k("right_ankle",0,-36,0).add(rt("right_foot"))))),U("left_hip_inner").add(k("left_hip",r/2-7,-e,0).add(k("left_knee",0,-40,0).add(k("left_ankle",0,-36,0).add(rt("left_foot")))))));new L(t).Create(),Q=t}function he(r,e,t){const n=new T(new Wt(P*5,P*5,P*5),new z({color:35071,transparent:!0,opacity:.5})),o=vt(e);return n.position.copy(Ks(r,o)),n.name=t,n}function la(r){return r.name in tt?Ae(r,tt[r.name].meshName):null}function Dt(r){return["right_shoulder_inner","left_shoulder_inner","right_hip_inner","left_hip_inner","five"].includes(r)}function ca(r){return!!(B.includes(r)||r==="right_hand"||r==="left_hand"||r==="right_foot"||r==="left_foot"||Dt(r)||F(r)||r.includes("_joint_sphere")||r.includes("_link_"))}function F(r){return ta(r)}const da=["torso","nose","neck","right_shoulder","left_shoulder","right_elbow","left_elbow","right_hip","left_hip","right_knee","left_knee","right_shoulder_inner","left_shoulder_inner","right_hip_inner","left_hip_inner","left_wrist_target","right_wrist_target","left_ankle_target","right_ankle_target"];function ue(r,e=!1){return!!(e&&B.includes(r)||da.includes(r)||F(r))}function me(r,e=!1){return e?["right_shoulder_inner","left_shoulder_inner","right_hip_inner","left_hip_inner","neck"].includes(r)==!1:!!r.endsWith("_target")}function st(r){return!!r.endsWith("_target")}function pe(r){return["left_hand","right_hand"].includes(r)}function ha(r){return["left_foot","right_foot"].includes(r)}function at(r){return["foot_mask","hand_mask"].includes(r)}function ua(r){return!!(r=="torso"||B.includes(r)||Dt(r)||r.includes("_joint_sphere")||r.includes("_link_"))}function fe(r){return["left_hand","right_hand","left_foot","right_foot"].includes(r)}const ma=[...Ct,"left_shoulder_inner","right_shoulder_inner","left_hip_inner","right_hip_inner","five","right_hand","left_hand","left_foot","right_foot","torso","left_wrist_target","right_wrist_target","left_ankle_target","right_ankle_target"];class L{constructor(e){p(this,"body");p(this,"part",{});this.body=e,this.body.traverse(t=>{ma.includes(t.name)&&(this.part[t.name]=t)}),this.part.left_shoulder_inner=this.getObjectByName("left_shoulder_inner"),this.part.right_shoulder_inner=this.getObjectByName("right_shoulder_inner"),this.part.left_hip_inner=this.getObjectByName("left_hip_inner"),this.part.right_hip_inner=this.getObjectByName("right_hip_inner"),this.part.five=this.getObjectByName("five"),this.part.right_hand=this.getObjectByName("right_hand"),this.part.left_hand=this.getObjectByName("left_hand"),this.part.right_foot=this.getObjectByName("right_foot"),this.part.left_foot=this.getObjectByName("left_foot"),this.part.torso=this.body}getObjectByName(e){const t=this.body.getObjectByName(e);if(!t)throw new Error(`Not found part: ${e}`);return t}getWorldPosition(e){const t=new y;return e.getWorldPosition(t),t}UpdateLink(e,t=this.BoneThickness,n=!1){["left_hip","right_hip","right_shoulder","left_shoulder"].includes(e)?Pt(this.part[`${e}_inner`],this.part[e],"neck",e,t,n):e!=="neck"&&B.includes(e)&&aa(this.part[e].parent,this.part[e],t,n)}get HeadSize(){return this.getWorldPosition(this.part.right_ear).distanceTo(this.getWorldPosition(this.part.left_ear))}set HeadSize(e){const t=e/this.HeadSize,n=this.part.left_ear.position.length()*t,o=this.part.left_eye.position.length()*t;this.part.left_ear.position.normalize().multiplyScalar(n),this.part.right_ear.position.normalize().multiplyScalar(n),this.part.left_eye.position.normalize().multiplyScalar(o),this.part.right_eye.position.normalize().multiplyScalar(o),this.UpdateLink("left_eye"),this.UpdateLink("right_eye"),this.UpdateLink("left_ear"),this.UpdateLink("right_ear")}get NoseToNeck(){return this.part.nose.position.length()}set NoseToNeck(e){this.part.nose.position.normalize().multiplyScalar(e),this.UpdateLink("nose")}get ShoulderToHip(){return this.getDistanceOf(this.getWorldPosition(this.part.five),this.getMidpoint(this.getWorldPosition(this.part.left_hip),this.getWorldPosition(this.part.right_hip)))}set ShoulderToHip(e){const t=this.ShoulderToHip;this.part.five.position.normalize().multiplyScalar(e/2),this.part.left_hip.position.multiplyScalar(e/t),this.part.right_hip.position.multiplyScalar(e/t),this.UpdateLink("left_hip"),this.UpdateLink("right_hip")}get ShoulderWidth(){return this.part.left_shoulder.position.distanceTo(this.part.right_shoulder.position)}set ShoulderWidth(e){const t=this.part.right_shoulder;t.position.x=-e/2;const n=this.part.left_shoulder;n.position.x=e/2,this.UpdateLink("right_shoulder"),this.UpdateLink("left_shoulder")}get UpperArm(){return this.part.left_elbow.position.length()}set UpperArm(e){this.part.left_elbow.position.normalize().multiplyScalar(e),this.part.right_elbow.position.normalize().multiplyScalar(e),this.UpdateLink("left_elbow"),this.UpdateLink("right_elbow")}get Forearm(){return this.part.left_wrist.position.length()}set Forearm(e){this.part.left_wrist.position.normalize().multiplyScalar(e),this.part.right_wrist.position.normalize().multiplyScalar(e),this.UpdateLink("left_wrist"),this.UpdateLink("right_wrist")}get ArmLength(){return this.UpperArm+this.Forearm}set ArmLength(e){const t=this.ArmLength;this.UpperArm=e*this.UpperArm/t,this.Forearm=e*this.Forearm/t}get Thigh(){return this.part.left_knee.position.length()}set Thigh(e){this.part.left_knee.position.normalize().multiplyScalar(e),this.part.right_knee.position.normalize().multiplyScalar(e),this.UpdateLink("left_knee"),this.UpdateLink("right_knee")}get HandSize(){return Math.abs(this.part.left_hand.scale.x)/N}set HandSize(e){const t=this.HandSize;this.part.left_hand.scale.divideScalar(t*N).multiplyScalar(e*N),this.part.right_hand.scale.divideScalar(t*N).multiplyScalar(e*N)}get Hips(){return this.getDistanceOf(this.getWorldPosition(this.part.left_hip),this.getWorldPosition(this.part.right_hip))}set Hips(e){const t=this.getWorldPosition(this.part.left_hip),n=this.getWorldPosition(this.part.right_hip),o=this.getMidpoint(t,n),s=t.sub(o).normalize().multiplyScalar(e/2).add(o),i=n.sub(o).normalize().multiplyScalar(e/2).add(o);this.setPositionFromWorld(this.part.left_hip,s),this.setPositionFromWorld(this.part.right_hip,i),this.UpdateLink("left_hip"),this.UpdateLink("right_hip")}get LowerLeg(){return this.part.left_ankle.position.length()}set LowerLeg(e){this.part.left_ankle.position.normalize().multiplyScalar(e),this.part.right_ankle.position.normalize().multiplyScalar(e),this.UpdateLink("left_ankle"),this.UpdateLink("right_ankle")}get LegLength(){return this.Thigh+this.LowerLeg}set LegLength(e){const t=this.LegLength;this.Thigh=e*this.Thigh/t,this.LowerLeg=e*this.LowerLeg/t}get FootSize(){return Math.abs(this.part.left_foot.scale.x)/G}set FootSize(e){const t=this.FootSize;this.part.left_foot.scale.divideScalar(t*G).multiplyScalar(e*G),this.part.right_foot.scale.divideScalar(t*G).multiplyScalar(e*G)}getLocalPosition(e,t){return e.worldToLocal(t.clone())}setPositionFromWorld(e,t){return e.position.copy(this.getLocalPosition(e.parent,t))}getDirectionVectorByParentOf(e,t,n){const o=this.part[e].parent,s=this.getLocalPosition(o,t);return this.getLocalPosition(o,n).clone().sub(s).normalize()}rotateTo(e,t){var d;const n=this.part[e],o=n.position.clone().normalize(),s=o.clone().cross(t),i=o.clone().angleTo(t);(d=n.parent)==null||d.rotateOnAxis(s.normalize(),i)}rotateTo3(e,t,n){this.rotateTo(e,this.getDirectionVectorByParentOf(e,t,n))}setPositionByDistance(e,t,n){const o=this.getDistanceOf(t,n);this.part[e].position.normalize().multiplyScalar(o)}setDirectionVector(e,t){const n=this.part[e].position.length();this.part[e].position.copy(t).multiplyScalar(n),this.UpdateLink(e)}getDistanceOf(e,t){return e.distanceTo(t)}getMidpoint(e,t){return e.clone().add(t).multiplyScalar(.5)}ResetPose(){Q==null||Q.traverse(e=>{if(e.name in this.part){const t=e.name;t=="torso"?this.part[t].position.setY(e.position.y):this.part[t].position.copy(e.position),this.part[t].rotation.copy(e.rotation),this.part[t].scale.copy(e.scale),this.UpdateLink(t)}})}SetBlazePose(e){this.ResetPose();const t=Object.fromEntries(Object.entries(Xs).map(([o,s])=>[o,new y().fromArray(e[s]??[0,0,0])])),n=[["five",[this.getMidpoint(this.getMidpoint(t.left_hip,t.right_hip),this.getMidpoint(t.left_shoulder,t.right_shoulder)),this.getMidpoint(t.left_shoulder,t.right_shoulder)]],["left_shoulder",[this.getMidpoint(t.left_shoulder,t.right_shoulder),"left_shoulder"]],["left_elbow",["left_shoulder","left_elbow"]],["left_wrist",["left_elbow","left_wrist"]],["left_hip",[this.getMidpoint(t.left_shoulder,t.right_shoulder),"left_hip"]],["left_knee",["left_hip","left_knee"]],["left_ankle",["left_knee","left_ankle"]],["right_shoulder",[this.getMidpoint(t.left_shoulder,t.right_shoulder),"right_shoulder"]],["right_elbow",["right_shoulder","right_elbow"]],["right_wrist",["right_elbow","right_wrist"]],["right_hip",[this.getMidpoint(t.left_shoulder,t.right_shoulder),"right_hip"]],["right_knee",["right_hip","right_knee"]],["right_ankle",["right_knee","right_ankle"]],["nose",[this.getMidpoint(t.left_shoulder,t.right_shoulder),"nose"]],["left_eye",["nose","left_eye"]],["right_eye",["nose","right_eye"]],["left_ear",["left_eye","left_ear"]],["right_ear",["right_eye","right_ear"]]];for(const[o,[s,i]]of n)this.rotateTo3(o,s instanceof y?s:t[s],i instanceof y?i:t[i]),this.setPositionByDistance(o,s instanceof y?s:t[s],i instanceof y?i:t[i]),this.UpdateLink(o);this.Update()}SetPose(e){this.ResetPose();const t=Object.fromEntries(Object.entries(Lt).map(([o,s])=>[o,new y().fromArray(e[s]??[0,0,0])]));this.part.torso.position.setY(this.getMidpoint(t.Hips,t.Chest).y),this.Hips=this.getDistanceOf(t.Hips,t.UpLeg_L)*2,this.Thigh=this.getDistanceOf(t.UpLeg_L,t.Leg_L),this.LowerLeg=this.getDistanceOf(t.Leg_L,t.Foot_L),this.UpperArm=this.getDistanceOf(t.Arm_L,t.ForeArm_L),this.Forearm=this.getDistanceOf(t.ForeArm_L,t.Hand_L),this.ShoulderWidth=2*(this.getDistanceOf(t.Shoulder_L,t.Arm_L)+this.getDistanceOf(t.Chest,t.Shoulder_L)/Math.SQRT2);const n=[["five",["Hips","Chest"]],["left_elbow",["Arm_L","ForeArm_L"]],["left_wrist",["ForeArm_L","Hand_L"]],["left_knee",["UpLeg_L","Leg_L"]],["left_ankle",["Leg_L","Foot_L"]],["right_elbow",["Arm_R","ForeArm_R"]],["right_wrist",["ForeArm_R","Hand_R"]],["right_knee",["UpLeg_R","Leg_R"]],["right_ankle",["Leg_R","Foot_R"]]];for(const[o,[s,i]]of n)this.rotateTo(o,this.getDirectionVectorByParentOf(o,t[s],t[i]));this.Update()}GetHandData(e){const t=this.part[e],n={child:{}};return t.traverse(o=>{if(o.name&&F(o.name)){o.name in n.child&&console.log("Duplicate name",o.name,o);const s={};this.getDistanceOf(o.position,new y(0,0,0))!=0&&(s.position=o.position.toArray()),this.getDistanceOf(o.scale,new y(1,1,1))!=0&&(s.scale=o.scale.toArray()),(o.rotation.x!==0||o.rotation.y!==0||o.rotation.z!==0)&&(s.rotation=o.rotation.toArray()),s&&(n.child[o.name]=s)}}),n}RestoreHand(e,t){var n;t.child=Object.fromEntries(Object.entries(t.child).map(([o,s])=>e=="left_hand"?[o.replace("_R","_L"),s]:e=="right_hand"?[o.replace("_L","_R"),s]:[o,s])),(n=this.part[e])==null||n.traverse(o=>{if(o.name&&o.name in t.child){const s=t.child[o.name];s.position&&o.position.fromArray(s.position),s.rotation&&o.rotation.fromArray(s.rotation),s.scale&&o.scale.fromArray(s.scale)}})}GetBodyData(){const e=this.part.torso,t={position:e.position.toArray(),rotation:e.rotation.toArray(),scale:e.scale.toArray(),child:{}};return e.traverse(n=>{if(n.name&&ca(n.name)){n.name in t.child&&console.log("Duplicate name",n.name,n);const o={};this.getDistanceOf(n.position,new y(0,0,0))!=0&&(o.position=n.position.toArray()),this.getDistanceOf(n.scale,new y(1,1,1))!=0&&(o.scale=n.scale.toArray()),(n.rotation.x!==0||n.rotation.y!==0||n.rotation.z!==0)&&(o.rotation=n.rotation.toArray()),o&&(t.child[n.name]=o)}}),t}RestoreBody(e){const t=this.part.torso;t==null||t.traverse(n=>{if(n.name&&n.name in e.child){const o=e.child[n.name];o.position&&n.position.fromArray(o.position),o.rotation&&n.rotation.fromArray(o.rotation),o.scale&&n.scale.fromArray(o.scale)}}),e.position&&t.position.fromArray(e.position),e.rotation&&t.rotation.fromArray(e.rotation),e.scale&&t.scale.fromArray(e.scale)}UpdateBones(e=this.BoneThickness){this.part.torso.traverse(t=>{if(t.name in this.part){const n=t.name;this.UpdateLink(n,e)}})}CreateBones(e=this.BoneThickness){this.part.torso.traverse(t=>{if(t.name in this.part){const n=t.name;this.UpdateLink(n,e,!0)}})}Create(){this.CreateBones(),this.part.torso.add(he(this.part.torso,this.part.left_wrist,"left_wrist_target")),this.part.torso.add(he(this.part.torso,this.part.right_wrist,"right_wrist_target")),this.part.torso.add(he(this.part.torso,this.part.left_ankle,"left_ankle_target")),this.part.torso.add(he(this.part.torso,this.part.right_ankle,"right_ankle_target"))}Get18keyPointsData(){return B.map(e=>e in this.part?this.getWorldPosition(this.part[e]).toArray():[0,0,0])}get BoneThickness(){var e;return Math.abs(((e=this.part.neck.getObjectByName("neck_joint_sphere"))==null?void 0:e.scale.x)??P)}set BoneThickness(e){this.UpdateBones(e)}GetIKSolver(){return new Zs([{target:this.part.left_wrist_target,effector:this.part.left_wrist,links:[{index:this.part.left_elbow,enabled:!0},{index:this.part.left_shoulder,enabled:!0}],iteration:10,minAngle:0,maxAngle:1},{target:this.part.right_wrist_target,effector:this.part.right_wrist,links:[{index:this.part.right_elbow,enabled:!0},{index:this.part.right_shoulder,enabled:!0}],iteration:10,minAngle:0,maxAngle:1},{target:this.part.left_ankle_target,effector:this.part.left_ankle,links:[{index:this.part.left_knee,enabled:!0},{index:this.part.left_hip,enabled:!0}],iteration:10,minAngle:0,maxAngle:1},{target:this.part.right_ankle_target,effector:this.part.right_ankle,links:[{index:this.part.right_knee,enabled:!0},{index:this.part.right_hip,enabled:!0}],iteration:10,minAngle:0,maxAngle:1}])}ResetTargetPosition(e,t){const n=this.part.torso,o=this.part[e],s=this.part[t],i=vt(o);s.position.copy(this.getLocalPosition(n,i))}ResetAllTargetsPosition(){this.ResetTargetPosition("left_wrist","left_wrist_target"),this.ResetTargetPosition("right_wrist","right_wrist_target"),this.ResetTargetPosition("left_ankle","left_ankle_target"),this.ResetTargetPosition("right_ankle","right_ankle_target")}Update(){this.ResetAllTargetsPosition(),this.UpdateBones(),this.part.torso.updateMatrixWorld(!0)}}const pa="_AlertDialogOverlay_4rriq_6",fa="_overlayShow_4rriq_1",ga="_AlertDialogContent_4rriq_14",_a="_contentShow_4rriq_1",ya="_AlertDialogTitle_4rriq_34",wa={AlertDialogOverlay:pa,overlayShow:fa,AlertDialogContent:ga,contentShow:_a,AlertDialogTitle:ya,"lds-roller":"_lds-roller_4rriq_64"},{AlertDialogOverlay:Sa,AlertDialogContent:ba,AlertDialogTitle:va,"lds-roller":Ca}=wa;function ka(){return f("div",{className:Ca,children:[a("div",{}),a("div",{}),a("div",{}),a("div",{}),a("div",{}),a("div",{}),a("div",{}),a("div",{})]})}const Oe=x.create(({title:r})=>{const e=La();return a(Zt,{defaultOpen:!0,open:e.visible,children:f(Yt,{children:[a(Jt,{className:Sa}),a(Qt,{className:ba,children:f(Xt,{className:va,children:[a(ka,{}),r]})})]})})});function La(){return ke(Oe)}function Te(r=0){const e=qt(t=>{x.show(Oe,t)},r);return{show:t=>{e(t)},hide:()=>{e.cancel(),Ma()}}}function Ma(){x.hide(Oe)}function D(r){const e=r==null?void 0:r.stack,t=`${h.t("Something went wrong!")}
${e||r}`;lo({title:h.t("Oops...")??"",description:t,children:a("a",{href:"https://github.com/ZhUyU1997/open-pose-editor/issues/new",children:h.t("If the problem persists, please click here to ask a question.")}),button:h.t("Close")??""})}const Pa="_ToastViewport_1gbkr_7",Ra="_ToastRoot_1gbkr_25",Da="_slideIn_1gbkr_1",Fa="_hide_1gbkr_1",xa="_swipeOut_1gbkr_1",$a="_ToastTitle_1gbkr_81",Ha="_ToastDescription_1gbkr_89",Ta="_ToastAction_1gbkr_97",Ba="_Button_1gbkr_101",Ea="_small_1gbkr_109",Aa="_large_1gbkr_115",Oa="_violet_1gbkr_121",Ia="_green_1gbkr_132",Ua={ToastViewport:Pa,ToastRoot:Ra,slideIn:Da,hide:Fa,swipeOut:xa,ToastTitle:$a,ToastDescription:Ha,ToastAction:Ta,Button:Ba,small:Ea,large:Aa,violet:Oa,green:Ia},{ToastViewport:Na,ToastRoot:Ga,ToastTitle:za,ToastAction:Va,Button:ja,small:Wa,green:Ka}=Ua,Ft=x.create(({title:r,button:e,duration:t=3e3})=>{const n=qa();return f(en,{swipeDirection:"right",duration:t,children:[f(tn,{className:Ga,defaultOpen:!0,open:n.visible,onOpenChange:o=>{o==!1&&n.hide()},children:[a(nn,{className:za,children:r}),e?a(on,{className:Va,asChild:!0,altText:"",children:a("button",{className:M(ja,Wa,Ka),onClick:()=>{n.resolve("action"),n.hide()},children:e})}):void 0]}),a(rn,{className:Na})]})});function qa(){return ke(Ft)}async function $(r){return await x.show(Ft,r)}const X=[];function Za(r,e){return r=Math.ceil(r),e=Math.floor(e),Math.floor(Math.random()*(e-r+1))+r}function it(){return X?X[Za(0,X.length-1)]:null}async function Ya(r){const t=await(await fetch(r)).arrayBuffer();console.log(t.byteLength);const n=new Int32Array(t),o=Object.keys(Lt).length;for(let s=0;s<n.length/(o*3);s++){const i=[];for(let d=0;d<o;d++){const c=n[s*(o*3)+d*3+0],l=n[s*(o*3)+d*3+1],u=n[s*(o*3)+d*3+2];i.push([c/1e3,l/1e3,u/1e3])}X==null||X.push(i)}}function Ja(){const r=navigator.userAgent.toLocaleLowerCase();return r.match(/tencenttraveler/)!=null||r.match(/qqbrowse/)!=null}class xt{constructor(e){p(this,"editor");this.editor=e}async DetectFromImage(e){if(Ja()){D("QQ浏览器暂不支持图片检测，请使用其他浏览器试试");return}if(!await this.editor.GetBodyToSetPose()){$({title:h.t("Please select a skeleton!!")});return}const n=Te(500);try{const o=await yt();if(!o)return;const s=await Ns(o);e(o),n.show({title:h.t("Downloading MediaPipe Pose Model")});const i=await Ws(s);if(n.hide(),i){if(!i.poseWorldLandmarks)throw new Error(JSON.stringify(i));const d=i.poseWorldLandmarks.map(({x:c,y:l,z:u})=>[c*100,-l*100,-u*100]);await this.editor.SetBlazePose(d);return}}catch(o){return n.hide(),o==="Timeout"?Ee()?D(`下载超时，请点击“从图片中检测 [中国]”或者开启魔法，再试一次。
`+o):D(o):D(h.t("If you try to detect anime characters, you may get an error. Please try again with photos.")+`
`+o),console.error(o),null}}async CopyKeypointToClipboard(){const e=await this.editor.GetBodyToSetPose();if(!e){$({title:h.t("Please select a skeleton!!")});return}try{const t=new L(e).Get18keyPointsData();await Ze(JSON.stringify(t,null,4)),$({title:h.t("Copied to Clipboard")})}catch(t){return D(t),console.error(t),null}}async SaveGesture(){if(!await this.editor.getSelectedHand()){$({title:h.t("Please select a hand!!")});return}try{this.editor.SaveGesture()}catch(t){return D(t),console.error(t),null}}async LoadGesture(){if(!await this.editor.getSelectedHand()){$({title:h.t("Please select a hand!!")});return}const t=await _t();if(t)try{this.editor.RestoreGesture(t)}catch(n){return D(n),console.error(n),null}}async GenerateSceneURL(){try{const e=encodeURIComponent(JSON.stringify(this.editor.GetSceneData())),n=`${location.href.replace(/#$/,"")}#${e}`;await Ze(n),$({title:h.t("Copied to Clipboard")})}catch(e){D(e),console.error(e)}}async SetRandomPose(){if(!await this.editor.GetBodyToSetPose()){$({title:h.t("Please select a skeleton!!")});return}const t=Te(500);try{let n=it();if(n){await this.editor.SetPose(n);return}if(t.show({title:h.t("Downloading Poses Library")}),await Ya(te["src/poses/data.bin"]),t.hide(),n=it(),n){await this.editor.SetPose(n);return}}catch(n){t.hide(),D(n),console.error(n);return}}async CopySkeleton(){if(!this.editor.getSelectedBody()){$({title:h.t("Please select a skeleton!!")});return}this.editor.CopySelectedBody()}async RemoveSkeleton(){if(!this.editor.getSelectedBody()){$({title:h.t("Please select a skeleton!!")});return}this.editor.RemoveBody()}FeedbackByQQ(){window.open("https://jq.qq.com/?_wv=1027&k=N6j4nigd")}FeedbackByGithub(){window.open("https://github.com/ZhUyU1997/open-pose-editor/issues/new")}}function ve(r="YYYY_MM_DD_HH_mm_ss"){return ht(new Date).format(r)}const lt=()=>({});function $t(){const[,r]=_.useState(lt);return _.useCallback(()=>{r(lt())},[])}const Qa=r=>{const e=_.useRef(r);return _.useLayoutEffect(()=>{e.current=r}),_.useMemo(()=>(...t)=>{const{current:n}=e;return n(...t)},[])};let Ce=!1;const Ie=(r,e,t="*")=>{console.log("return",{target:e,origin:t,data:r}),e==null||e.postMessage({cmd:"openpose-3d",...r},{targetOrigin:t})},Xa=r=>{if(Ce)return;const{parent:e}=window;if(!e)throw new Error("Parent window has closed");Ie(r,e)},ei=r=>{const{opener:e}=window;if(!e)throw new Error("Opener window has closed");Ie(r,e)},ti=r=>{if(Ce)return;const{opener:e,parent:t}=window;if(!e&&!t)throw new Error("window has closed");t&&Xa(r),e&&ei(r)};function ni(r){const e=_.useRef(),t=_.useRef(null);e.current="",t.current=null;const n=s=>Ie(s,t.current,e.current),o=Qa(async({origin:s,source:i,data:d})=>{if(!d)return;const{method:c,payload:l,type:u}=d;if(u=="call"){if(console.log("method",c,l),l&&Array.isArray(l)===!1){console.error("payload is not array");return}if(t.current=i,e.current=s,c in r){const S=r[c];if(typeof S=="function"){Ce=!0;const m=S(...l??[]),g=m instanceof Promise?await m:m;try{n({method:c,type:"return",payload:g})}catch(w){console.log(w)}Ce=!1}}else c==="GetAPIs"&&n({method:c,type:"return",payload:Object.keys(r)})}});_.useEffect(()=>(window.addEventListener("message",o),()=>window.removeEventListener("message",o)),[o])}class ie{constructor(){p(this,"eventHandlers",[])}AddEventListener(e){this.eventHandlers.push(e)}RemoveEventListener(e){this.eventHandlers=this.eventHandlers.filter(t=>t!==e)}TriggerEvent(e){this.eventHandlers.forEach(t=>t(e))}}function ct(r){return{scale:r.scale.clone(),rotation:r.rotation.clone(),position:r.position.clone()}}class oi{constructor(e){p(this,"scene");p(this,"camera");p(this,"canvas");p(this,"renderer");p(this,"orbitControls");this.scene=e.scene,this.camera=e.camera,this.canvas=e.canvas,this.orbitControls=e.orbitControls,e.renderer?this.renderer=e.renderer:this.renderer=new He({antialias:!0,canvas:e.canvas})}renderBySize(e,t,n){const o={aspect:this.camera.aspect};this.camera.aspect=e/t,this.camera.updateProjectionMatrix(),this.renderer.setSize(e,t,!0),n(e,t),this.camera.aspect=o.aspect,this.camera.updateProjectionMatrix()}GetCameraData(){return{position:this.camera.position.toArray(),rotation:this.camera.rotation.toArray(),target:this.orbitControls.target.toArray(),near:this.camera.near,far:this.camera.far,zoom:this.camera.zoom}}RestoreCamera(e,t=!0){this.camera.position.fromArray(e.position),this.camera.rotation.fromArray(e.rotation),this.camera.near=e.near,this.camera.far=e.far,this.camera.zoom=e.zoom,this.camera.updateProjectionMatrix(),e.target&&this.orbitControls.target.fromArray(e.target),t&&this.orbitControls.update()}changeView(e){if(!e)return()=>{};const t=this.GetCameraData();return this.RestoreCamera(e,!1),()=>{this.RestoreCamera(t)}}render(e,t,n,o){const s=()=>{this.renderer.render(this.scene,this.camera)},i=this.changeView(n);this.renderBySize(e,t,o??s),i()}}class ri{constructor({canvas:e,previewCanvas:t,parentElem:n=document,statsElem:o}){p(this,"renderer");p(this,"outputRenderer");p(this,"previewRenderer");p(this,"scene");p(this,"gridHelper");p(this,"axesHelper");p(this,"camera");p(this,"orbitControls");p(this,"transformControl");p(this,"dlight");p(this,"alight");p(this,"raycaster",new sn);p(this,"IsClick",!1);p(this,"stats");p(this,"composer");p(this,"finalComposer");p(this,"effectSobel");p(this,"enableComposer",!1);p(this,"enablePreview",!0);p(this,"enableHelper",!0);p(this,"paused",!1);p(this,"parentElem");p(this,"clearColor",11184810);p(this,"commandHistory",[]);p(this,"historyIndex",-1);p(this,"ikSolver");p(this,"saveSelectedBodyControlor");p(this,"autoSize",!0);p(this,"outputWidth",0);p(this,"outputHeight",0);p(this,"SelectEventManager",new ie);p(this,"UnselectEventManager",new ie);p(this,"ContextMenuEventManager",new ie);p(this,"PreviewEventManager",new ie);p(this,"LockViewEventManager",new ie);p(this,"isMoveMode",!1);p(this,"FreeMode",!0);p(this,"onlyHand",!1);p(this,"cameraDataOfView");this.parentElem=n,this.renderer=new He({canvas:e,antialias:!0}),this.outputRenderer=new He({antialias:!0}),this.outputRenderer.domElement.style.display="none",document.body.appendChild(this.outputRenderer.domElement),this.renderer.setClearColor(this.clearColor,0),this.scene=new an,this.gridHelper=new ln(8e3,200),this.axesHelper=new cn(1e3),this.scene.add(this.gridHelper),this.scene.add(this.axesHelper);const s=window.innerWidth/window.innerHeight;this.camera=new dn(60,s,.1,1e4),this.camera.position.set(0,100,200),this.camera.lookAt(0,100,0),this.camera.updateProjectionMatrix(),this.orbitControls=new hn(this.camera,this.renderer.domElement),this.orbitControls.target=new y(0,100,0),this.orbitControls.update(),this.transformControl=new un(this.camera,this.renderer.domElement),this.transformControl.setMode("rotate"),this.transformControl.setSize(.4),this.transformControl.setSpace("local"),this.registerTranformControlEvent(),this.scene.add(this.transformControl),this.previewRenderer=new oi({scene:this.scene,camera:this.camera,orbitControls:this.orbitControls,canvas:t}),this.dlight=new mn(16777215,1),this.dlight.position.set(0,160,1e3),this.scene.add(this.dlight),this.alight=new pn(16777215,.5),this.scene.add(this.alight),this.onMouseDown=this.onMouseDown.bind(this),this.onMouseMove=this.onMouseMove.bind(this),this.onMouseUp=this.onMouseUp.bind(this),this.handleResize=this.handleResize.bind(this),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleKeyUp=this.handleKeyUp.bind(this),this.addEvent(),this.initEdgeComposer(),o&&(this.stats=fn(),o.appendChild(this.stats.dom)),this.animate=this.animate.bind(this),this.animate(),this.handleResize(),this.AutoSaveScene()}disponse(){this.pause(),this.removeEvent(),this.renderer.dispose(),this.outputRenderer.dispose(),console.log("BodyEditor disponse")}pushCommand(e){console.log("pushCommand"),this.historyIndex!=this.commandHistory.length-1&&(this.commandHistory=this.commandHistory.slice(0,this.historyIndex+1)),this.commandHistory.push(e),this.historyIndex=this.commandHistory.length-1}CreateTransformCommand(e,t){const n=t,o=ct(e),s=new L(this.getBodyByPart(e));return{execute:()=>{e.position.copy(o.position),e.rotation.copy(o.rotation),e.scale.copy(o.scale),s.Update()},undo:()=>{e.position.copy(n.position),e.rotation.copy(n.rotation),e.scale.copy(n.scale),s.Update()}}}CreateAllTransformCommand(e,t){const n=t,o=this.getBodyByPart(e),s=new L(o),i=s.GetBodyData();return{execute:()=>{s.RestoreBody(i),s.Update()},undo:()=>{s.RestoreBody(n),s.Update()}}}CreateAddBodyCommand(e){return{execute:()=>{this.scene.add(e)},undo:()=>{e.removeFromParent(),this.DetachTransfromControl()}}}CreateRemoveBodyCommand(e){return{execute:()=>{e.removeFromParent(),this.DetachTransfromControl()},undo:()=>{this.scene.add(e)}}}Undo(){console.log("Undo",this.historyIndex),this.historyIndex>=0&&(this.commandHistory[this.historyIndex].undo(),this.historyIndex--)}Redo(){console.log("Redo",this.historyIndex),this.historyIndex<this.commandHistory.length-1&&(this.commandHistory[this.historyIndex+1].execute(),this.historyIndex++)}handleKeyDown(e){this.paused||(e.code==="KeyZ"&&(e.ctrlKey||e.metaKey)&&e.shiftKey?this.Redo():e.code==="KeyY"&&(e.ctrlKey||e.metaKey)?(this.Redo(),e.preventDefault()):e.code==="KeyZ"&&(e.ctrlKey||e.metaKey)?this.Undo():e.code==="KeyD"&&e.shiftKey?this.CopySelectedBody():e.key==="Delete"?this.RemoveBody():e.code==="KeyX"&&(this.MoveMode=!0))}handleKeyUp(e){this.paused||e.code==="KeyX"&&(this.MoveMode=!1)}registerTranformControlEvent(){let e={scale:new y,rotation:new gn,position:new y},t={};this.transformControl.addEventListener("change",()=>{const n=this.getSelectedBody();n&&new L(n).UpdateBones()}),this.transformControl.addEventListener("objectChange",()=>{}),this.transformControl.addEventListener("mouseDown",()=>{const n=this.getSelectedPart();if(n){e=ct(n);const o=this.getBodyByPart(n);t=new L(o).GetBodyData()}this.orbitControls.enabled=!1}),this.transformControl.addEventListener("mouseUp",()=>{var o;const n=this.getSelectedPart();n&&(st(n.name)?this.pushCommand(this.CreateAllTransformCommand(n,t)):this.pushCommand(this.CreateTransformCommand(n,e))),this.orbitControls.enabled=!0,(o=this.saveSelectedBodyControlor)==null||o.Update()})}updateSelectedBodyIKSolver(){var t,n,o,s;const e=this.getSelectedBody()??void 0;e!==this.saveSelectedBodyControlor&&(this.saveSelectedBodyControlor=e?new L(e):void 0,this.ikSolver=e?(t=this.saveSelectedBodyControlor)==null?void 0:t.GetIKSolver():void 0),me(((n=this.getSelectedPart())==null?void 0:n.name)??"")?(o=this.ikSolver)==null||o.update():(s=this.saveSelectedBodyControlor)==null||s.ResetAllTargetsPosition()}render(e=this.Width,t=this.Height){this.updateSelectedBodyIKSolver(),this.renderer.setViewport(0,0,e,t),this.renderer.setScissor(0,0,e,t),this.renderer.setScissorTest(!0),this.renderer.render(this.scene,this.camera)}get OutputWidth(){return this.autoSize?this.Width:this.outputWidth===0?this.Height:this.outputWidth}set OutputWidth(e){this.autoSize=!1,this.outputWidth=e}get OutputHeight(){return this.autoSize?this.Height:this.outputHeight===0?this.Height:this.outputHeight}set OutputHeight(e){this.autoSize=!1,this.outputHeight=e}renderPreview(){const e=this.OutputWidth,t=this.OutputHeight,n=e/t,o=2,[s,i,d,c]=n>o?[this.Width-50-150*o,220,150*o,150*o*t/e]:[this.Width-50-150*e/t,220,150*e/t,150],l={viewport:new Ne,scissor:new Ne,scissorTest:this.renderer.getScissorTest(),aspect:this.camera.aspect};this.renderer.getViewport(l.viewport),this.renderer.getScissor(l.viewport),this.renderer.setViewport(s,i,d,c),this.renderer.setScissor(s,i,d,c),this.renderer.setScissorTest(!0),this.camera.aspect=d/c,this.camera.updateProjectionMatrix();const u=this.changeView();this.renderer.render(this.scene,this.camera),u(),this.renderer.setViewport(l.viewport),this.renderer.setScissor(l.scissor),this.renderer.setScissorTest(l.scissorTest),this.camera.aspect=l.aspect,this.camera.updateProjectionMatrix()}renderOutputBySize(e,t,n){const o={aspect:this.camera.aspect};this.camera.aspect=e/t,this.camera.updateProjectionMatrix(),this.outputRenderer.setSize(e,t,!0),n(e,t),this.camera.aspect=o.aspect,this.camera.updateProjectionMatrix()}renderOutput(e=1,t){const n=this.OutputWidth*e,o=this.OutputHeight*e,s=()=>{this.outputRenderer.render(this.scene,this.camera)};this.renderOutputBySize(n,o,t??s)}getOutputPNG(){return this.outputRenderer.domElement.toDataURL("image/png")}animate(){var e;this.paused||(requestAnimationFrame(this.animate),this.handleResize(),this.render(),this.outputPreview(),(e=this.stats)==null||e.update())}outputPreview(){this.enablePreview&&this.CapturePreview(),this.PreviewEventManager.TriggerEvent(this.enablePreview)}pause(){this.paused=!0}resume(){this.paused=!1,this.animate()}getAncestors(e){const t=[];return e.traverseAncestors(n=>t.push(n)),t}getBodyByPart(e){return(e==null?void 0:e.name)==="torso"?e:this.getAncestors(e).find(n=>(n==null?void 0:n.name)==="torso")??null}triggerSelectEvent(e){const t=new L(e);this.SelectEventManager.TriggerEvent(t),this.UpdateBones()}triggerUnselectEvent(){this.UnselectEventManager.TriggerEvent(),this.UpdateBones()}addEvent(){this.renderer.domElement.addEventListener("mousedown",this.onMouseDown,!1),this.renderer.domElement.addEventListener("mousemove",this.onMouseMove,!1),this.renderer.domElement.addEventListener("mouseup",this.onMouseUp,!1),this.renderer.domElement.addEventListener("resize",this.handleResize),this.parentElem.addEventListener("keydown",this.handleKeyDown),this.parentElem.addEventListener("keyup",this.handleKeyUp)}removeEvent(){this.renderer.domElement.removeEventListener("mousedown",this.onMouseDown,!1),this.renderer.domElement.removeEventListener("mousemove",this.onMouseMove,!1),this.renderer.domElement.removeEventListener("mouseup",this.onMouseUp,!1),this.renderer.domElement.removeEventListener("resize",this.handleResize),this.parentElem.removeEventListener("keydown",this.handleKeyDown),this.parentElem.removeEventListener("keyup",this.handleKeyUp)}onMouseDown(e){this.IsClick=!0}onMouseMove(e){e.movementX==0&&e.movementY==0||(this.IsClick=!1)}onMouseUp(e){const t=e.offsetX-this.renderer.domElement.offsetLeft,n=e.offsetY-this.renderer.domElement.offsetTop;this.raycaster.setFromCamera({x:t/this.renderer.domElement.clientWidth*2-1,y:-(n/this.renderer.domElement.clientHeight)*2+1},this.camera);const o=this.raycaster.intersectObjects(this.GetBodies(),!0),s=o.find(l=>l.object.name==="red_point"),i=s?s.object:o.length>0?o[0].object:null,d=i?i.name:"";let c=i;if(console.log(c==null?void 0:c.name),this.IsClick){if(e.button===2||e.which===3){console.log("Right mouse button released"),this.ContextMenuEventManager.TriggerEvent({mouseX:t,mouseY:n});return}if(!c){this.DetachTransfromControl(),this.triggerUnselectEvent();return}if(this.MoveMode){if(ue(d,this.FreeMode)||(c=this.getAncestors(c).find(u=>ue(u.name,this.FreeMode))??null),c&&me(c.name,this.FreeMode)===!1&&(c=this.getBodyByPart(c)),c){console.log(c.name),this.transformControl.setMode("translate"),this.transformControl.setSpace("world"),this.transformControl.attach(c);const u=this.getBodyByPart(c);u&&this.triggerSelectEvent(u)}}else if(ue(d,this.FreeMode)||(c=this.getAncestors(c).find(u=>ue(u.name,this.FreeMode))??null),c){console.log(c.name),me(c.name)?(this.transformControl.setMode("translate"),this.transformControl.setSpace("world")):(this.transformControl.setMode("rotate"),this.transformControl.setSpace("local")),this.transformControl.attach(c);const u=this.getBodyByPart(c);u&&this.triggerSelectEvent(u)}}}traverseHandObjecct(e){this.GetBodies().forEach(t=>{t.traverse(n=>{pe(n==null?void 0:n.name)&&e(n)})})}traverseBodies(e){this.GetBodies().forEach(t=>{t.traverse(n=>{e(n)})})}traverseBones(e){this.GetBodies().forEach(t=>{t.traverse(n=>{n instanceof Ge&&F(n.name)&&e(n)})})}traverseExtremities(e){this.GetBodies().forEach(t=>{t.traverse(n=>{fe(n.name)&&e(n)})})}onlyShowSkeleton(){const e=[];return this.traverseBodies(t=>{ua(t.name)===!1&&t.visible==!0&&(t.visible=!1,e.push(t))}),()=>{e.forEach(t=>t.visible=!0)}}showMask(){const e=[];return this.scene.traverse(t=>{at(t.name)&&(console.log(t.name),t.visible=!0,e.push(t))}),()=>{e.forEach(t=>t.visible=!1)}}hideSkeleten(){const e=new Map;return this.GetBodies().forEach(t=>{t.traverse(n=>{fe(n==null?void 0:n.name)?(e.set(n,n.parent),this.scene.attach(n)):(n==null?void 0:n.name)==="red_point"&&(n.visible=!1)}),t.visible=!1}),e}GetBodies(){return this.scene.children.filter(e=>(e==null?void 0:e.name)==="torso")}showSkeleten(e){for(const[t,n]of e.entries())n==null||n.attach(t);e.clear(),this.GetBodies().forEach(t=>{t.traverse(n=>{(n==null?void 0:n.name)==="red_point"&&(n.visible=!0)}),t.visible=!0})}changeComposer(e){const t=this.enableComposer;return this.enableComposer=e,()=>this.enableComposer=t}changeHandMaterialTraverse(e){const t=new Map;return this.scene.traverse(n=>{if(!fe(n.name))return;const o=la(n);t.set(o,o.material),e=="depth"?o.material=new ze:e=="normal"?o.material=new Ve:e=="phone"&&(o.material=new we)}),()=>{for(const[n,o]of t.entries())n.material=o;t.clear()}}changeHandMaterial(e){return e=="depth"?this.scene.overrideMaterial=new ze:e=="normal"?this.scene.overrideMaterial=new Ve:e=="phone"&&(this.scene.overrideMaterial=new we),()=>{this.scene.overrideMaterial=null}}getCameraLookAtVector(){const e=new y(0,0,-1);return e.applyQuaternion(this.camera.quaternion),e}getZDistanceFromCamera(e){const t=this.getCameraLookAtVector().normalize();return e.clone().sub(this.camera.position).dot(t)}changeCamera(){let e=[];this.scene.traverse(l=>{this.OnlyHand?pe(l==null?void 0:l.name)&&e.push(l):fe(l==null?void 0:l.name)&&e.push(l)}),e=this.objectInView(e);const t=new y;this.camera.getWorldPosition(t);const o=e.map(l=>{const u=new y;return l.getWorldPosition(u),u}).map(l=>this.getZDistanceFromCamera(l)),s=Math.min(...o),i=Math.max(...o),d=this.camera.near,c=this.camera.far;return this.camera.near=Math.max(s-20,0),this.camera.far=Math.max(i+20,20),console.log("camera",this.camera.near,this.camera.far),this.camera.updateProjectionMatrix(),()=>{this.camera.near=d,this.camera.far=c,this.camera.updateProjectionMatrix()}}Capture(){const e=this.onlyShowSkeleton();this.renderOutput();const t=this.getOutputPNG();return e(),t}CapturePreview(){const e=window.devicePixelRatio*140/this.OutputHeight,t=this.OutputWidth*e,n=this.OutputHeight*e;this.previewRenderer.render(t,n,this.cameraDataOfView)}CaptureCanny(){return this.renderOutput(1,(e,t)=>{var s,i;this.changeComposerResoultion(e,t);const n=this.changeHandMaterialTraverse("normal"),o=this.showMask();(s=this.composer)==null||s.render(),o(),(i=this.finalComposer)==null||i.render(),n()}),this.getOutputPNG()}CaptureNormal(){const e=this.changeHandMaterial("normal");return this.renderOutput(),e(),this.getOutputPNG()}CaptureDepth(){const e=this.changeHandMaterial("depth"),t=this.changeCamera();return this.renderOutput(),t(),e(),this.getOutputPNG()}changeTransformControl(){const e=this.getSelectedPart();return e?(this.DetachTransfromControl(),()=>{this.transformControl.attach(e)}):()=>{}}changeHelper(){const e={axesHelper:this.axesHelper.visible,gridHelper:this.gridHelper.visible};return this.axesHelper.visible=!1,this.gridHelper.visible=!1,()=>{this.axesHelper.visible=e.axesHelper,this.gridHelper.visible=e.gridHelper}}MakeImages(){this.renderer.setClearColor(0);const e=this.changeHelper(),t=this.changeTransformControl(),n=this.changeView(),o=this.Capture(),s=this.hideSkeleten(),i=this.CaptureDepth(),d=this.CaptureNormal(),c=this.CaptureCanny();this.showSkeleten(s),this.renderer.setClearColor(0,0),e(),t(),n();const l={pose:o,depth:i,normal:d,canny:c};return ti({method:"MakeImages",type:"event",payload:l}),l}CopySelectedBody(){const e=this.GetBodies(),t=this.getSelectedBody();if(!t&&e.length!==0)return;const n=e.length===0?de():ee(t);n&&(this.pushCommand(this.CreateAddBodyCommand(n)),e.length!==0&&(n.position.x+=10),this.scene.add(n),this.fixFootVisible(),this.transformControl.setMode("translate"),this.transformControl.setSpace("world"),this.transformControl.attach(n))}CopyBodyZ(){const e=de();if(!e)return;const t=this.GetBodies().filter(n=>n.position.x===0).map(n=>Math.ceil(n.position.z/30));t.length>0&&e.translateZ((Math.min(...t)-1)*30),this.pushCommand(this.CreateAddBodyCommand(e)),this.scene.add(e),this.fixFootVisible()}CopyBodyX(){const e=de();if(!e)return;const t=this.GetBodies().filter(n=>n.position.z===0).map(n=>Math.ceil(n.position.x/50));t.length>0&&e.translateX((Math.min(...t)-1)*50),this.pushCommand(this.CreateAddBodyCommand(e)),this.scene.add(e),this.fixFootVisible()}getSelectedBody(){let e=this.getSelectedPart()??null;return e=e?this.getBodyByPart(e):null,e}getSelectedPart(){return this.transformControl.object}getHandByPart(e){return pe(e==null?void 0:e.name)?e:this.getAncestors(e).find(n=>pe(n==null?void 0:n.name))??null}getSelectedHand(){let e=this.getSelectedPart()??null;return e=e?this.getHandByPart(e):null,e}RemoveBody(){const e=this.getSelectedBody();e&&(this.pushCommand(this.CreateRemoveBodyCommand(e)),console.log(e.name),e.removeFromParent(),this.DetachTransfromControl())}pointsInView(e){this.camera.updateMatrix(),this.camera.updateMatrixWorld();const t=new je().setFromProjectionMatrix(new $e().multiplyMatrices(this.camera.projectionMatrix,this.camera.matrixWorldInverse));return e.filter(n=>t.containsPoint(n))}getBouningSphere(e){const t=new _n().setFromObject(e,!0),n=new y;return t.getCenter(n),t.getBoundingSphere(new yn(n))}objectInView(e){this.camera.updateMatrix(),this.camera.updateMatrixWorld();const t=new je().setFromProjectionMatrix(new $e().multiplyMatrices(this.camera.projectionMatrix,this.camera.matrixWorldInverse));return e.filter(n=>{const o=this.getBouningSphere(n);return t.intersectsSphere(o)})}get MoveMode(){return this.isMoveMode}set MoveMode(e){var o;let t=e;this.isMoveMode=e;const n=((o=this.getSelectedPart())==null?void 0:o.name)??"";if(e)if(me(n,this.FreeMode))t=!0;else{const s=this.getSelectedBody();s&&this.transformControl.attach(s)}else st(n)&&(t=!0);t?(this.transformControl.setMode("translate"),this.transformControl.setSpace("world")):(this.transformControl.setMode("rotate"),this.transformControl.setSpace("local"))}get Width(){return this.renderer.domElement.clientWidth}get Height(){return this.renderer.domElement.clientHeight}get OnlyHand(){return this.onlyHand}set OnlyHand(e){this.onlyHand=e,this.setFootVisible(!this.onlyHand)}get EnableHelper(){return this.enableHelper}set EnableHelper(e){this.enableHelper=e,this.gridHelper.visible=e,this.axesHelper.visible=e}setFootVisible(e){this.traverseExtremities(t=>{ha(t.name)&&(t.visible=e)})}fixFootVisible(){this.setFootVisible(!this.OnlyHand)}handleResize(){const e=new wn;if(this.renderer.getSize(e),e.width==this.Width&&e.height===this.Height)return;const t=this.renderer.domElement;t.clientWidth==0||t.clientHeight==0||(this.camera.aspect=t.clientWidth/t.clientHeight,this.camera.updateProjectionMatrix(),this.renderer.setSize(t.clientWidth,t.clientHeight,!1))}initEdgeComposer(){this.composer=new We(this.outputRenderer);const e=new Sn(this.scene,this.camera);this.composer.addPass(e),this.composer.renderToScreen=!1;const t=new Me(new bn({uniforms:{baseTexture:{value:null},bloomTexture:{value:this.composer.renderTarget2.texture}},vertexShader:`
varying vec2 vUv;
void main() {
    vUv = uv;
    gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );

}`,fragmentShader:`
uniform sampler2D baseTexture;
uniform sampler2D bloomTexture;

varying vec2 vUv;

void main() {
    vec4 bloomColor = texture2D(bloomTexture, vUv);
    float grayValue = dot(bloomColor.rgb, vec3(0.299, 0.587, 0.114));
    vec4 baseColor = texture2D(baseTexture, vUv);
    vec4 masked = vec4(baseColor.rgb * step(0.001, grayValue), 1.0);
    gl_FragColor = step(0.5, masked)  * vec4(1.0); // Binarization
    // gl_FragColor = bloomColor;
}

`,defines:{}}),"baseTexture");t.needsSwap=!0,this.finalComposer=new We(this.outputRenderer),this.finalComposer.addPass(e);const n=new Me(vn);this.finalComposer.addPass(n);const o=new Me(Cn);o.uniforms.resolution.value.x=this.Width*window.devicePixelRatio,o.uniforms.resolution.value.y=this.Height*window.devicePixelRatio,this.finalComposer.addPass(o),this.effectSobel=o,this.finalComposer.addPass(t)}changeComposerResoultion(e,t){var n,o;(n=this.composer)==null||n.setSize(e,t),(o=this.finalComposer)==null||o.setSize(e,t),this.effectSobel&&(this.effectSobel.uniforms.resolution.value.x=e*window.devicePixelRatio,this.effectSobel.uniforms.resolution.value.y=t*window.devicePixelRatio)}get CameraNear(){return this.camera.near}set CameraNear(e){this.camera.near=e,this.camera.updateProjectionMatrix()}get CameraFar(){return this.camera.far}set CameraFar(e){this.camera.far=e,this.camera.updateProjectionMatrix()}get CameraFocalLength(){return this.camera.getFocalLength()}set CameraFocalLength(e){this.camera.setFocalLength(e)}GetCameraData(){return{position:this.camera.position.toArray(),rotation:this.camera.rotation.toArray(),target:this.orbitControls.target.toArray(),near:this.camera.near,far:this.camera.far,zoom:this.camera.zoom}}GetSceneData(){return{header:"Openpose Editor by Yu Zhu",version:"0.1.23",object:{bodies:this.GetBodies().map(n=>new L(n).GetBodyData()),camera:this.GetCameraData()},setting:{}}}GetGesture(){const e=this.getSelectedHand(),t=this.getSelectedBody();return!e||!t?null:{header:"Openpose Editor by Yu Zhu",version:"0.1.23",object:{hand:new L(t).GetHandData(e.name==="left_hand"?"left_hand":"right_hand")},setting:{}}}AutoSaveScene(){try{const e=localStorage.getItem("AutoSaveSceneData");e&&localStorage.setItem("LastSceneData",e),setInterval(()=>{localStorage.setItem("AutoSaveSceneData",JSON.stringify(this.GetSceneData()))},5e3)}catch(e){console.error(e)}}SaveScene(){try{qe(JSON.stringify(this.GetSceneData()),`scene_${ve()}.json`)}catch(e){console.error(e)}}RestoreGesture(e){const t=JSON.parse(e),{version:n,object:{hand:o},setting:s}=t;if(!o)throw new Error("Invalid json");const i=this.getSelectedHand(),d=this.getSelectedBody();if(!i||!d)throw new Error("!hand || !body");new L(d).RestoreHand(i.name=="left_hand"?"left_hand":"right_hand",o)}SaveGesture(){const e=this.GetGesture();if(!e)throw new Error("Failed to get gesture");qe(JSON.stringify(e),`gesture_${ve()}.json`)}ClearScene(){this.GetBodies().forEach(e=>e.removeFromParent())}CreateBodiesFromData(e){return e.map(t=>{const n=de();return new L(n).RestoreBody(t),n})}RestoreCamera(e,t=!0){this.camera.position.fromArray(e.position),this.camera.rotation.fromArray(e.rotation),this.camera.near=e.near,this.camera.far=e.far,this.camera.zoom=e.zoom,this.camera.updateProjectionMatrix(),e.target&&this.orbitControls.target.fromArray(e.target),t&&this.orbitControls.update()}RestoreScene(e){try{if(!e)return;const t=JSON.parse(e),{version:n,object:{bodies:o,camera:s},setting:i}=t,d=this.CreateBodiesFromData(o);this.ClearScene(),d.length>0&&this.scene.add(...d);for(const c of d)new L(c).ResetAllTargetsPosition();this.RestoreCamera(s)}catch(t){D(t),console.error(t)}}ResetScene(){try{this.ClearScene(),this.CopySelectedBody();const e=this.getSelectedBody();e&&(this.scene.add(e),this.dlight.target=e)}catch(e){D(e),console.error(e)}}RestoreLastSavedScene(){const e=localStorage.getItem("LastSceneData");e&&this.RestoreScene(e)}async LoadScene(){const e=await _t();e&&this.RestoreScene(e)}async GetBodyToSetPose(){const e=this.GetBodies();return e.length==1?e[0]:this.getSelectedBody()}async SetPose(e){const t=await this.GetBodyToSetPose();if(!t)return;const n=new L(t),o=n.GetBodyData();n.SetPose(e),this.pushCommand(this.CreateAllTransformCommand(t,o))}async SetBlazePose(e){const t=await this.GetBodyToSetPose();if(!t)return;const n=new L(t),o=n.GetBodyData();n.SetBlazePose(e),this.pushCommand(this.CreateAllTransformCommand(t,o))}DetachTransfromControl(){this.transformControl.detach(),this.triggerUnselectEvent()}LockView(){this.cameraDataOfView=this.GetCameraData(),this.LockViewEventManager.TriggerEvent(!0)}UnlockView(){this.cameraDataOfView=void 0,this.LockViewEventManager.TriggerEvent(!1)}RestoreView(){this.cameraDataOfView&&this.RestoreCamera(this.cameraDataOfView)}changeView(){if(this.cameraDataOfView){const e=this.GetCameraData();return this.RestoreCamera(this.cameraDataOfView,!1),()=>{this.RestoreCamera(e)}}return()=>{}}getSelectedBone(){const e=this.getSelectedPart();return e&&F(e.name)?e:null}UpdateBones(){const d=(l,u,S=16711680)=>{const m=l.children.find(g=>g instanceof T&&!at(g.name));if(m){const g=m.material;g.color.set(S),g.opacity=u,g.needsUpdate=!0}},c=this.getSelectedBone();if(this.traverseBones(l=>{d(l,c?.2:1)}),c){let l=c;for(d(l,1,15658496),l.traverseAncestors(u=>{F(u.name)&&d(u,1)});;){const u=l.children.filter(S=>S instanceof Ge);if(u.length!==1)break;d(u[0],1),l=u[0]}}}}async function si(){const r=Te(500);r.show({title:h.t("Downloading Hand Model")}),await na(te["models/hand.fbx"]),r.show({title:h.t("Downloading Foot Model")}),await oa(te["models/foot.fbx"]),r.hide(),ia()}function ai(r,e,t){const[n,o]=_.useState();return _.useEffect(()=>{const s=r.current;if(!s)return;const i=e.current;if(!i)return;console.warn("create editor");let d=new ri({canvas:s,previewCanvas:i,parentElem:(t==null?void 0:t.current)??document,statsElem:void 0});return o(d),(async()=>{if(d&&(await si(),d==null||d.ResetScene(),d!=null&&d.RestoreScene&&location.hash)){const l=decodeURIComponent(location.hash.replace(/^#/,""));d==null||d.RestoreScene(l),location.hash=""}})(),()=>{console.warn("disponse"),d==null||d.disponse(),d=null}},[]),n}function ii(){const[r]=_.useState(()=>Re[h.language]??"English"),[e]=_.useState(()=>Object.fromEntries(Object.entries(Re).map(([t,n])=>[n,t])));return{current:r,languagList:[...Object.values(Re)],changeLanguage:t=>{const n=e[t],o=new URL(window.location.href);o.searchParams.set("lng",n),window.location.assign(o)}}}const li="_Root_ofrr6_5",ci="_ContextMenuContent_ofrr6_13",di="_slideDownAndFade_ofrr6_1",hi="_ContextMenuItem_ofrr6_32",ui="_ContextMenuSeparator_ofrr6_52",mi="_IconButton_ofrr6_58",pi="_RightSlot_ofrr6_80",fi="_slideUpAndFade_ofrr6_1",gi="_slideRightAndFade_ofrr6_1",_i="_slideLeftAndFade_ofrr6_1",yi={Root:li,ContextMenuContent:ci,slideDownAndFade:di,ContextMenuItem:hi,ContextMenuSeparator:ui,IconButton:mi,RightSlot:pi,slideUpAndFade:fi,slideRightAndFade:gi,slideLeftAndFade:_i},{Root:wi,ContextMenuContent:Si,ContextMenuItem:H,RightSlot:ge}=yi,bi=x.create(({editor:r,mouseX:e,mouseY:t,onChangeBackground:n})=>{const o=_.useMemo(()=>new xt(r),[r]),s=ke();return a("div",{className:wi,style:{display:s.visible?void 0:"none"},onClick:()=>{s.hide()},onContextMenu:i=>{i.preventDefault()},children:f("div",{className:Si,style:{top:t,left:e},children:[f("div",{className:H,onClick:()=>{r.Undo()},children:[h.t("Undo"),a("div",{className:ge,children:"⌘ Z"})]}),f("div",{className:H,onClick:()=>{r.Redo()},children:[h.t("Redo"),a("div",{className:ge,children:"⇧ ⌘ Z"})]}),f("div",{className:H,onClick:()=>{o.CopySkeleton()},children:[h.t("Duplicate Skeleton"),a("div",{className:ge,children:"⇧ D"})]}),f("div",{className:H,onClick:()=>{o.RemoveSkeleton()},children:[h.t("Delete Skeleton"),a("div",{className:ge,children:h.t("Del")})]}),a("div",{className:H,onClick:()=>{o.CopyKeypointToClipboard()},children:h.t("Copy Keypoint Data")}),a("div",{className:H,onClick:()=>{o.SetRandomPose()},children:h.t("Set Random Pose")}),a("div",{className:H,onClick:()=>o.DetectFromImage(n),children:h.t("Detect From Image")}),Ee()?a("div",{className:H,onClick:()=>{bt(!1),o.DetectFromImage(n)},children:h.t("Detect From Image")+" [中国]"}):void 0]})})});async function vi(r){return await x.show(bi,r)}const{MenubarRoot:Ci,MenubarTrigger:I,MenubarContent:q,MenubarItem:v,MenubarCheckboxItem:le,MenubarRadioItem:ki,MenubarItemIndicator:Z,MenubarSeparator:xe,RightSlot:_e,Blue:Li,inset:R}=Us,Mi=({editor:r,onChangeBackground:e,onScreenShot:t,style:n})=>{const o=$t(),s=_.useMemo(()=>new xt(r),[r]),{current:i,changeLanguage:d,languagList:c}=ii();return _.useEffect(()=>{const l=u=>{vi({...u,editor:r,onChangeBackground:e})};return r==null||r.ContextMenuEventManager.AddEventListener(l),()=>{r==null||r.ContextMenuEventManager.RemoveEventListener(l)}},[r]),f(kn,{className:Ci,style:n,children:[f(A,{children:[a(O,{className:I,children:h.t("File")}),a(j,{children:f(W,{className:q,align:"start",sideOffset:5,alignOffset:-3,children:[a(b,{className:v,onSelect:()=>r.ResetScene(),children:h.t("Reset Scene")}),a(b,{className:v,onSelect:()=>r.LoadScene(),children:h.t("Load Scene")}),a(b,{className:v,onSelect:()=>r.SaveScene(),children:h.t("Save Scene")}),a(b,{className:v,onSelect:()=>s.LoadGesture(),children:h.t("Load Gesture")}),a(b,{className:v,onSelect:()=>s.SaveGesture(),children:h.t("Save Gesture")}),a(b,{className:v,onSelect:()=>{s.GenerateSceneURL()},children:h.t("Generate Scene URL")}),a(Pe,{className:xe}),a(b,{className:v,onSelect:()=>r.RestoreLastSavedScene(),children:h.t("Restore Last Scene")}),a(Pe,{className:xe}),a(b,{className:v,onSelect:()=>s.DetectFromImage(e),children:h.t("Detect From Image")}),Ee()?a(b,{className:v,onSelect:()=>{bt(!1),s.DetectFromImage(e)},children:h.t("Detect From Image")+" [中国]"}):void 0,a(b,{className:v,onSelect:()=>s.SetRandomPose(),children:h.t("Set Random Pose")}),a(b,{className:v,onSelect:async()=>{const l=await yt();l&&e(l)},children:h.t("Set Background Image")}),f(b,{className:v,children:["v","0.1.23"]})]})})]}),f(A,{children:[a(O,{className:I,children:h.t("Edit")}),a(j,{children:f(W,{className:q,align:"start",sideOffset:5,alignOffset:-3,children:[f(b,{className:v,onSelect:()=>r.Undo(),children:[h.t("Undo"),a("div",{className:_e,children:"⌘ Z"})]}),f(b,{className:v,onSelect:()=>r.Redo(),children:[h.t("Redo"),a("div",{className:_e,children:"⇧ ⌘ Z"})]}),a(Pe,{className:xe}),f(b,{className:v,onSelect:()=>r.CopySelectedBody(),children:[h.t("Duplicate Skeleton"),a("div",{className:_e,children:"⇧ D"})]}),f(b,{className:v,onSelect:()=>r.RemoveBody(),children:[h.t("Delete Skeleton"),a("div",{className:_e,children:h.t("Del")})]})]})})]}),f(A,{children:[a(O,{className:I,children:h.t("View")}),a(j,{children:f(W,{className:q,align:"start",sideOffset:5,alignOffset:-14,children:[a(b,{className:M(v,R),onSelect:()=>{r.LockView()},children:h.t("Lock View")}),a(b,{className:M(v,R),onSelect:()=>{r.UnlockView()},children:h.t("Unlock View")}),a(b,{className:M(v,R),onSelect:()=>{r.RestoreView()},children:h.t("Restore View")})]})})]}),f(A,{children:[a(O,{className:I,children:h.t("Setting")}),a(j,{children:f(W,{className:q,align:"start",sideOffset:5,alignOffset:-14,children:[f(oe,{className:M(le,R),checked:r.MoveMode,onCheckedChange:()=>{r.MoveMode=!r.MoveMode,o()},children:[a(K,{className:Z,children:a(re,{})}),h.t("Move Mode")]}),f(oe,{className:M(le,R),checked:r.FreeMode,onCheckedChange:()=>{r.FreeMode=!r.FreeMode,o()},children:[a(K,{className:Z,children:a(re,{})}),h.t("Free Mode")]}),f(oe,{className:M(le,R),checked:r.OnlyHand,onCheckedChange:()=>{r.OnlyHand=!r.OnlyHand,o()},children:[a(K,{className:Z,children:a(re,{})}),h.t("Only Hand")]}),f(oe,{className:M(le,R),checked:r.enablePreview,onCheckedChange:()=>{r.enablePreview=!r.enablePreview,o()},children:[a(K,{className:Z,children:a(re,{})}),h.t("Show Preview")]}),f(oe,{className:M(le,R),checked:r.EnableHelper,onCheckedChange:()=>{r.EnableHelper=!r.EnableHelper,o()},children:[a(K,{className:Z,children:a(re,{})}),h.t("Show Grid")]})]})})]}),f(A,{children:[a(O,{className:I,children:h.t("Feedback")}),a(j,{children:f(W,{className:q,align:"start",sideOffset:5,alignOffset:-14,children:[a(b,{className:M(v,R),onSelect:()=>{s.FeedbackByGithub()},children:"Github"}),a(b,{className:M(v,R),onSelect:()=>{s.FeedbackByQQ()},children:"QQ"})]})})]}),f(A,{children:[a(O,{className:I,children:"Language"}),a(j,{children:a(W,{className:q,align:"start",sideOffset:5,alignOffset:-14,children:a(Ln,{value:i,onValueChange:l=>{d(l)},children:c.map(l=>f(Mn,{className:M(ki,R),value:l,children:[a(K,{className:Z,children:a(Pn,{})}),l]},l))})})})]}),a(A,{children:a(O,{className:M(I,Li),onClick:async()=>{const l=r.MakeImages(),u=Object.fromEntries(Object.entries(l).map(([S,m])=>[S,{src:m,title:S+"_"+ve()}]));t(u)},children:h.t("Generate")})})]})},Pi="_PopoverContent_1f240_5",Ri="_slideDownAndFade_1f240_1",Di="_slideLeftAndFade_1f240_1",Fi="_slideUpAndFade_1f240_1",xi="_slideRightAndFade_1f240_1",$i="_PopoverArrow_1f240_35",Hi="_IconButton_1f240_39",Ti={PopoverContent:Pi,slideDownAndFade:Ri,slideLeftAndFade:Di,slideUpAndFade:Fi,slideRightAndFade:xi,PopoverArrow:$i,IconButton:Hi},Bi="_SliderRoot_1ofoi_4",Ei="_SliderTrack_1ofoi_13",Ai="_SliderRange_1ofoi_21",Oi={SliderRoot:Bi,SliderTrack:Ei,SliderRange:Ai},{SliderRoot:Ii,SliderTrack:Ui,SliderRange:Ni}=Oi,Gi=({range:r,value:e,onValueChange:t,onValueCommit:n,style:o})=>a("form",{style:{...o},children:a(Rn,{className:Ii,value:[e],min:r[0],max:r[1],step:(r[1]-r[0])/150,onValueChange:([s])=>{t==null||t(s)},onValueCommit:([s])=>{n==null||n(s)},children:a(Dn,{className:Ui,children:a(Fn,{className:Ni})})})}),{PopoverContent:zi,IconButton:Vi,PopoverArrow:ji,Input:Wi}=Ti,dt=({type:r,name:e,range:t,getValue:n,onChange:o,onValueCommit:s,forceUpdate:i})=>{const d=n(),[c,l]=_.useState(()=>r=="int"?d.toString():n().toFixed(2));return _.useEffect(()=>{l(r=="int"?d.toString():n().toFixed(2))},[d]),f("div",{style:{display:"flex",justifyContent:"flex-end"},children:[a("div",{style:{minWidth:60,maxWidth:120,overflow:"hidden",color:"gray",fontSize:"70%",marginInlineEnd:10,textAlign:"end",textOverflow:"ellipsis"},children:e}),a(Gi,{range:t,value:n(),onValueChange:u=>{r=="int"?o==null||o(Math.round(u)):o==null||o(u),i()},onValueCommit:s,style:{width:150}},e),a("input",{className:Wi,style:{marginInlineStart:10,width:60,height:20,color:"gray",fontSize:"70%"},value:c,onChange:u=>{const S=u.target.value;l(S)},onBlur:()=>{try{let u=parseFloat(c);if(isNaN(u))throw"Is NaN";u=Math.max(Math.min(u,t[1]),t[0]),console.log(u),o==null||o(u)}catch{console.log("invalid input"),l(d.toString())}}})]})};function Ki(r){const e={OutputWidth:{type:"int",range:[128,3e3],name:h.t("Width")},OutputHeight:{type:"int",range:[128,3e3],name:h.t("Height")},CameraNear:{range:[.1,2e3],name:h.t("Camera Near")},CameraFar:{range:[.1,2e4],name:h.t("Camera Far")},CameraFocalLength:{range:[.1,100],name:h.t("Camera Focal Length")}};return Object.entries(e).map(([t,{type:n,range:o,name:s}])=>({type:n,name:s,range:o,getValue(){const i=r[t];return isNaN(i)?o[0]:i},onChange(i){r[t]=i}}))}function qi(r){const e={BoneThickness:{range:[.1,3],name:h.t("Bone Thickness")},HeadSize:{range:[.1,100],name:h.t("Head Size")},NoseToNeck:{range:[.1,100],name:h.t("Nose To Neck")},ShoulderWidth:{range:[.1,100],name:h.t("Shoulder Width")},ShoulderToHip:{range:[.1,100],name:h.t("Shoulder To Hip")},ArmLength:{range:[.1,100],name:h.t("Arm Length")},Forearm:{range:[.1,100],name:h.t("Forearm")},UpperArm:{range:[.1,100],name:h.t("Upper Arm")},HandSize:{range:[.1,10],name:h.t("Hand Size")},Hips:{range:[.1,100],name:h.t("Hips")},LegLength:{range:[.1,100],name:h.t("Leg Length")},Thigh:{range:[.1,100],name:h.t("Thigh")},LowerLeg:{range:[.1,100],name:h.t("Lower Leg")},FootSize:{range:[.1,10],name:h.t("Foot Size")}};function t(c,l,u,S,m){console.log(S,m);const g={execute:()=>{l[u]=m,l.Update()},undo:()=>{l[u]=S,l.Update()}};g.execute(),c.pushCommand(g)}let n=r.getSelectedBody(),o=n?new L(n):null;const s=()=>{const c=r.getSelectedBody();return c!==n&&(n=c,o=c?new L(c):null),o};let i=0,d=!1;return Object.entries(e).map(([c,{type:l,range:u,name:S}])=>({type:l,name:S,range:u,getValue:()=>{const m=c,g=s();return g?g[m]:-1},onChange(m){const g=c,w=s();w&&(d==!1&&(i=w[g]),d=!0,w[g]=m)},onValueCommit(m){const g=c,w=s();w&&(d=!1,t(r,w,g,i,m),w[g]=m)}}))}const Zi=({editor:r,style:e})=>{const t=$t(),[n,o]=_.useState(!0),s=_.useMemo(()=>Ki(r),[r]),i=_.useMemo(()=>qi(r),[r]),[d,c]=_.useState(!1);return _.useEffect(()=>{const l=()=>{c(!0)},u=()=>{c(!1)};return r.SelectEventManager.AddEventListener(l),r.UnselectEventManager.AddEventListener(u),()=>{r.SelectEventManager.RemoveEventListener(l),r.UnselectEventManager.RemoveEventListener(u)}},[r]),f(xn,{open:n,children:[a($n,{asChild:!0,children:a("button",{className:Vi,style:e,onClick:()=>o(l=>!l),children:a(Hn,{})})}),a(Tn,{children:f(Bn,{className:zi,sideOffset:5,children:[f("div",{style:{display:"flex",flexDirection:"column",gap:10},children:[s.map((l,u)=>a(dt,{...l,forceUpdate:t},u)),d?f(Gn,{children:[a("div",{style:{fontSize:15,marginTop:10,marginBottom:8},children:h.t("Body Parameters")}),i.map((l,u)=>a(dt,{...l,forceUpdate:t},u))]}):void 0]}),a(En,{className:ji})]})})]})},{app:Yi,threejsCanvas:Ji,gallery:Qi,background:Xi}=Ms,el=pt.forwardRef(({isLock:r,enable:e,onChange:t,onRestore:n,onRun:o},s)=>f("div",{style:{position:"relative",display:e?"flex":"none",justifyContent:"center",backgroundColor:"gray"},children:[a("canvas",{ref:s,style:{objectFit:"contain",width:"unset",maxHeight:"100%",maxWidth:300}}),a(An,{style:{position:"absolute",top:-10,right:5,backgroundColor:"white",borderRadius:10,padding:5},onClick:()=>{o()}}),a(r?In:Un,{style:{position:"absolute",top:20,right:5,backgroundColor:"white",borderRadius:10,padding:5},onClick:()=>{t(!r)}}),a(On,{style:{position:"absolute",top:50,right:5,backgroundColor:r?"white":"gray",borderRadius:10,padding:5},onClick:()=>{r&&n()}})]}));function tl(){const r=_.useRef(null),e=_.useRef(null),t=_.useRef(null),n=ai(r,e,t),[o,s]=_.useState(()=>({pose:{title:"",src:""},depth:{title:"",src:""},normal:{title:"",src:""},canny:{title:"",src:""}})),i=_.useCallback(m=>{const g=t.current;g&&(g.style.backgroundImage=m?`url(${m})`:"none")},[]),d=_.useCallback(m=>{s(m)},[]),[c,l]=_.useState(!1),[u,S]=_.useState(!1);return _.useEffect(()=>{const m=w=>{l(w)},g=w=>{S(w)};return n==null||n.PreviewEventManager.AddEventListener(m),n==null||n.LockViewEventManager.AddEventListener(g),()=>{n==null||n.PreviewEventManager.RemoveEventListener(m),n==null||n.LockViewEventManager.RemoveEventListener(g)}},[n]),ni({GetAppVersion:()=>"0.1.23",MakeImages:()=>n==null?void 0:n.MakeImages(),Pause:()=>n==null?void 0:n.pause(),Resume:()=>n==null?void 0:n.resume(),OutputWidth:m=>n&&typeof m=="number"?(n.OutputWidth=m,!0):!1,OutputHeight:m=>n&&typeof m=="number"?(n.OutputHeight=m,!0):!1,OnlyHand(m){return n&&typeof m=="boolean"?(n.OnlyHand=m,!0):!1},MoveMode(m){return n&&typeof m=="boolean"?(n.MoveMode=m,!0):!1},GetWidth:()=>n==null?void 0:n.Width,GetHeight:()=>n==null?void 0:n.Height,GetSceneData:()=>n==null?void 0:n.GetSceneData(),LockView:()=>n==null?void 0:n.LockView(),UnlockView:()=>n==null?void 0:n.UnlockView(),RestoreView:()=>n==null?void 0:n.RestoreView()}),f("div",{ref:t,className:Xi,children:[a("canvas",{className:Ji,tabIndex:-1,ref:r,onContextMenu:m=>{m.preventDefault()}}),f("div",{className:Qi,children:[a(el,{enable:c,ref:e,isLock:u,onChange:m=>{m?n==null||n.LockView():n==null||n.UnlockView()},onRestore:()=>{n==null||n.RestoreView()},onRun:async()=>{if(!n)return;const m=n.MakeImages(),g=Object.fromEntries(Object.entries(m).map(([w,V])=>[w,{src:V,title:w+"_"+ve()}]));d(g)}}),Object.entries(o).map(([m,{src:g,title:w}])=>a("img",{...g?{src:g}:{},title:w,onClick:V=>{const C=V.target,E=(C==null?void 0:C.getAttribute("title"))??"",ne=(C==null?void 0:C.getAttribute("src"))??"";ft(ne,E)}},m))]}),a("div",{className:Yi,style:{pointerEvents:"none"},children:a("div",{style:{pointerEvents:"initial",marginTop:10,display:"flex",justifyContent:"center"},children:n?a(Mi,{editor:n,onChangeBackground:i,onScreenShot:d}):void 0})}),n?a(Zi,{editor:n,style:{pointerEvents:"initial",position:"fixed",top:10,right:10}}):void 0]})}function nl(){Nn.createRoot(document.getElementById("root")).render(a(pt.StrictMode,{children:a(x.Provider,{children:a(tl,{})})}))}nl();
