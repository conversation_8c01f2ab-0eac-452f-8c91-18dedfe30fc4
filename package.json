{"name": "open-pose-editor", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode online", "dev-extension": "yarpm run build-extension && pipenv run start", "build": "tsc && vite build --mode online", "build-singlefile": "tsx tools/generate_assets.ts && tsc && vite build --mode singlefile && tsx tools/fix-singlefile.ts ", "build-extension": "tsc && vite build --mode extension-editor && vite build --mode extension-entry", "generate_downloads": "tsx tools/generate_downloads.ts ", "preview": "vite preview", "format": "prettier --write src", "translate": "tsx tools/translate.ts"}, "dependencies": {"@ebay/nice-modal-react": "^1.2.9", "@mediapipe/pose": "^0.5.1675469404", "@radix-ui/colors": "^0.1.8", "@radix-ui/react-alert-dialog": "^1.0.3", "@radix-ui/react-dialog": "^1.0.3", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-menubar": "^1.0.2", "@radix-ui/react-popover": "^1.0.5", "@radix-ui/react-slider": "^1.1.1", "@radix-ui/react-toast": "^1.1.3", "@react-hookz/web": "^23.0.0", "@typescript-eslint/eslint-plugin": "^5.54.1", "@typescript-eslint/parser": "^5.54.1", "browser-fs-access": "^0.33.0", "classnames": "^2.3.2", "dat.gui": "^0.7.9", "dayjs": "^1.11.7", "eslint": "^8.35.0", "eslint-config-prettier": "^8.7.0", "fs-extra": "^11.1.0", "i18next": "^22.4.11", "i18next-browser-languagedetector": "^7.0.1", "lodash-es": "^4.17.21", "prettier": "^2.8.4", "react": "^18.2.0", "react-dom": "^18.2.0", "rollup-plugin-visualizer": "^5.9.0", "three": "^0.150.1", "tsx": "^3.12.5", "type-fest": "^3.6.1", "vite-plugin-pwa": "^0.14.4", "workbox-window": "^6.5.4", "yarpm": "^1.2.0"}, "devDependencies": {"@types/dat.gui": "^0.7.7", "@types/fs-extra": "^11.0.1", "@types/lodash-es": "^4.17.7", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@types/three": "^0.149.0", "@vitejs/plugin-react": "^3.1.0", "typescript": "^4.9.3", "vite": "^4.1.0", "vite-plugin-conditional-compiler": "^0.1.1", "zip-a-folder": "^1.1.5"}}