@import '@radix-ui/colors/blackA.css';
@import '@radix-ui/colors/mauve.css';
@import '@radix-ui/colors/violet.css';

.Root {
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
}

.ContextMenuContent {
    animation-name: slideDownAndFade;
    position: fixed;
    top: 0px;
    left: 0px;
}

.ContextMenuContent {
    min-width: 220px;
    background-color: white;
    border-radius: 6px;
    padding: 5px;
    box-shadow: 0px 10px 38px -10px rgba(22, 23, 24, 0.35),
        0px 10px 20px -15px rgba(22, 23, 24, 0.2);
    animation-duration: 400ms;
    animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
    will-change: transform, opacity;
}

.ContextMenuItem {
    font-size: 13px;
    line-height: 1;
    color: var(--violet11);
    border-radius: 3px;
    display: flex;
    align-items: center;
    height: 25px;
    padding: 0 5px;
    position: relative;
    padding-left: 25px;
    user-select: none;
    outline: none;
}

.ContextMenuItem:hover {
    background-color: var(--violet9);
    color: var(--violet1);
}

.ContextMenuSeparator {
    height: 1px;
    background-color: var(--violet6);
    margin: 5px;
}

.IconButton {
    all: unset;
    font-family: inherit;
    border-radius: 100%;
    height: 35px;
    width: 35px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: var(--violet11);
    background-color: white;
    box-shadow: 0 2px 10px var(--blackA7);
    width: 100%;
    height: 100%;
}
.IconButton:hover {
    background-color: var(--violet3);
}
.IconButton:focus {
    box-shadow: 0 0 0 2px black;
}

.RightSlot {
    margin-left: auto;
    padding-left: 20px;
    color: var(--mauve11);
}

.ContextMenuItem:hover > .RightSlot {
    color: white;
}

@keyframes slideUpAndFade {
    from {
        opacity: 0;
        transform: translateY(2px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideRightAndFade {
    from {
        opacity: 0;
        transform: translateX(-2px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideDownAndFade {
    from {
        opacity: 0;
        transform: translateY(-2px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideLeftAndFade {
    from {
        opacity: 0;
        transform: translateX(2px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}
