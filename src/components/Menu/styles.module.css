@import '@radix-ui/colors/blackA.css';
@import '@radix-ui/colors/mauve.css';
@import '@radix-ui/colors/violet.css';

.MenubarRoot {
    display: flex;
    background-color: white;
    padding: 3px;
    border-radius: 6px;
    box-shadow: 0 2px 10px var(--blackA7);
    font-family: var(--openpose-editor-font-family);
}

.MenubarTrigger {
    all: unset;
    padding: 8px 12px;
    outline: none;
    user-select: none;
    font-weight: 500;
    line-height: 1;
    border-radius: 4px;
    color: var(--violet11);
    font-size: 13px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2px;
}

.MenubarTrigger[data-highlighted],
.MenubarTrigger[data-state='open'] {
    background-color: var(--violet4);
}

.MenubarTrigger.Blue {
    background-color: var(--violet9);
    color: white;
}

.MenubarContent,
.MenubarSubContent {
    min-width: 220px;
    background-color: white;
    border-radius: 6px;
    padding: 5px;
    box-shadow: 0px 10px 38px -10px rgba(22, 23, 24, 0.35),
        0px 10px 20px -15px rgba(22, 23, 24, 0.2);
    animation-duration: 400ms;
    animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
    will-change: transform, opacity;
}

.MenubarItem,
.MenubarSubTrigger,
.MenubarCheckboxItem,
.MenubarRadioItem {
    all: unset;
    font-size: 13px;
    line-height: 1;
    color: var(--violet11);
    border-radius: 4px;
    display: flex;
    align-items: center;
    height: 25px;
    padding: 0 10px;
    position: relative;
    user-select: none;
    font-family: var(--openpose-editor-font-family);
}

.MenubarItem.inset,
.MenubarSubTrigger.inset,
.MenubarCheckboxItem.inset,
.MenubarRadioItem.inset {
    padding-left: 20px;
}

.MenubarItem[data-state='open'],
.MenubarSubTrigger[data-state='open'] {
    background-color: var(--violet4);
    color: var(--violet11);
}

.MenubarItem[data-highlighted],
.MenubarSubTrigger[data-highlighted],
.MenubarCheckboxItem[data-highlighted],
.MenubarRadioItem[data-highlighted] {
    background-image: linear-gradient(
        135deg,
        var(--violet9) 0%,
        var(--violet10) 100%
    );
    color: var(--violet1);
}

.MenubarItem[data-disabled],
.MenubarSubTrigger[data-disabled],
.MenubarCheckboxItem[data-disabled],
.MenubarRadioItem[data-disabled] {
    color: var(--mauve8);
    pointer-events: none;
}

.MenubarItemIndicator {
    position: absolute;
    left: 0;
    width: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.MenubarSeparator {
    height: 1px;
    background-color: var(--violet6);
    margin: 5px;
}

.RightSlot {
    margin-left: auto;
    padding-left: 20px;
    color: var(--mauve9);
}

[data-highlighted] > .RightSlot {
    color: white;
}

[data-disabled] > .RightSlot {
    color: var(--mauve8);
}
