@import '@radix-ui/colors/blackA.css';
@import '@radix-ui/colors/mauve.css';
@import '@radix-ui/colors/violet.css';

.PopoverContent {
    border-radius: 4px;
    padding: 20px;
    /* width: 260px; */
    background-color: white;
    box-shadow: hsl(206 22% 7% / 35%) 0px 10px 38px -10px,
        hsl(206 22% 7% / 20%) 0px 10px 20px -15px;
    animation-duration: 400ms;
    animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
    will-change: transform, opacity;
    font-family: var(--openpose-editor-font-family);
}

.PopoverContent:focus {
    box-shadow: hsl(206 22% 7% / 35%) 0px 10px 38px -10px,
        hsl(206 22% 7% / 20%) 0px 10px 20px -15px, 0 0 0 2px var(--violet7);
}
.PopoverContent[data-state='open'][data-side='top'] {
    animation-name: slideDownAndFade;
}
.PopoverContent[data-state='open'][data-side='right'] {
    animation-name: slideLeftAndFade;
}
.PopoverContent[data-state='open'][data-side='bottom'] {
    animation-name: slideUpAndFade;
}
.PopoverContent[data-state='open'][data-side='left'] {
    animation-name: slideRightAndFade;
}

.PopoverArrow {
    fill: white;
}

.IconButton {
    all: unset;
    font-family: inherit;
    border-radius: 100%;
    height: 35px;
    width: 35px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: var(--violet11);
    background-color: white;
    box-shadow: 0 2px 10px var(--blackA7);
}
.IconButton:hover {
    background-color: var(--violet3);
}
.IconButton:focus {
    box-shadow: 0 0 0 2px var(--violet6);
}

@keyframes slideUpAndFade {
    from {
        opacity: 0;
        transform: translateY(2px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideRightAndFade {
    from {
        opacity: 0;
        transform: translateX(-2px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideDownAndFade {
    from {
        opacity: 0;
        transform: translateY(-2px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideLeftAndFade {
    from {
        opacity: 0;
        transform: translateX(2px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

Input {
    background-color: var(--violet5);
    border: none;
    box-sizing: border-box;
    padding-left: 5px;
    padding-right: 5px;
    border-radius: 7px;
}

Input:focus {
    border: 2px var(--violet11) solid;
    border-radius: 5;
}
