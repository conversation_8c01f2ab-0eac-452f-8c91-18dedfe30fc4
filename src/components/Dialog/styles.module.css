@import '@radix-ui/colors/blackA.css';
@import '@radix-ui/colors/green.css';
@import '@radix-ui/colors/mauve.css';
@import '@radix-ui/colors/violet.css';

.DialogOverlay {
    background-color: var(--blackA9);
    position: fixed;
    inset: 0;
    animation: overlayShow 150ms cubic-bezier(0.16, 1, 0.3, 1);
    font-family: var(--openpose-editor-font-family);
}

.DialogContent {
    font-family: var(--openpose-editor-font-family);
    background-color: white;
    border-radius: 6px;
    box-shadow: hsl(206 22% 7% / 35%) 0px 10px 38px -10px,
        hsl(206 22% 7% / 20%) 0px 10px 20px -15px;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90vw;
    max-width: 450px;
    max-height: 85vh;
    padding: 25px;
    animation: contentShow 150ms cubic-bezier(0.16, 1, 0.3, 1);
}
.DialogContent:focus {
    outline: none;
}

.DialogTitle {
    margin: 0;
    font-weight: 500;
    color: var(--mauve12);
    font-size: 32px;
}

.DialogDescription {
    margin: 10px 0 20px;
    color: var(--mauve11);
    font-size: 15px;
    line-height: 1.5;
    overflow-y: auto;
    word-break: break-all;
    max-height: 60vh;
}

.Button {
    all: unset;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    padding: 0 15px;
    font-size: 15px;
    line-height: 1;
    font-weight: 500;
    height: 35px;
}
.Button.violet {
    background-color: white;
    color: var(--violet11);
    box-shadow: 0 2px 10px var(--blackA7);
}
.Button.violet:hover {
    background-color: var(--mauve3);
}
.Button.violet:focus {
    box-shadow: 0 0 0 2px black;
}
.Button.green {
    background-color: var(--green4);
    color: var(--green11);
}
.Button.green:hover {
    background-color: var(--green5);
}
.Button.green:focus {
    box-shadow: 0 0 0 2px var(--green7);
}

.IconButton {
    all: unset;
    font-family: inherit;
    border-radius: 100%;
    height: 25px;
    width: 25px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: var(--violet11);
    position: absolute;
    top: 10px;
    right: 10px;
}
.IconButton:hover {
    background-color: var(--violet4);
}
.IconButton:focus {
    box-shadow: 0 0 0 2px var(--violet7);
}

@keyframes overlayShow {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes contentShow {
    from {
        opacity: 0;
        transform: translate(-50%, -48%) scale(0.96);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}
