# Online 3D Openpose Editor [[English](README.md)] [[中文版](README-zh.md)]
# [エディターを表示するにはここをクリックしてください](https://zhuyu1997.github.io/open-pose-editor/)
# プレビュー
https://user-images.githubusercontent.com/18410894/224073965-c782a05f-0a20-41cf-863d-8de490021ed7.mp4
# WebUIの拡張機能としてインストール

1. WebUIの「Extension」タブを開きます
2. 「Available」タブを開きます
3. WebUIが古い場合は「Extension index URL」を `https://raw.githubusercontent.com/AUTOMATIC1111/stable-diffusion-webui-extensions/master/index.json` に変更してください
4. 「Load from:」ボタンをクリックします
5. 3D Openpose Editorの「Install」ボタンをクリックします
6. 「Installed」タブを開き、「Apply and restart UI」ボタンをクリックします

# 特徴

- **ポーズの編集**: マウスで関節を選択し、回転させることで、3Dモデルのポーズを編集できます。

- **手の編集**: 手のボーンを選択し、色付きの円を調整することで、手の位置を微調整できます。

- **深度/法線/Cannyマップ**: 深度、法線、Cannyマップを生成して可視化することで、AIの描画力を向上させることができます。

- **シーンの保存/読み込み/復元**: シーンの保存・読み込み機能が備わっているため、作成中のシーンを保存して後で復元できます。

- **身体パラメータの調整**: 高さ、幅、脚の長さなどの身体パラメータを調整して、カスタム3Dモデルを作成できます。
# 使い方
### カメラのナビゲーション:
- **カメラの回転**: 空いている場所を左クリックしながらマウスを移動します。
- **カメラの移動**: 空いている場所を右クリックしながらマウスを移動します。

### スケルトンの操作:
- **関節の回転**: 関節を左クリックで選択してから、色付きの円のうちの1つを押しながらマウスを移動します。
- **手の編集**: 赤い点をクリックして手のボーンを選択し、色付きの円のうちの1つを押しながらマウスを移動します。
### 身体パラメータの調整:
- **スケルトンの選択**: スケルトンをクリックして選択します。
- **身体パラメータを開く**: メニューの「身体パラメータ」をクリックして、パラメータを調整します。
### 出力解像度の調整:
- **メニューで出力解像度を調整**: メニューの「幅」または「高さ」を変更して、出力解像度を調整します。
### その他の機能:
- **移動モードに切り替え**: Xキーを押すと、移動モードに切り替わり、身体全体を移動できます。
- **ボディの削除**: Dキーを押して、身体全体を削除できます。
# 問い合わせ
Email: <EMAIL>

[Tencent QQ Group](https://jq.qq.com/?_wv=1027&k=N6j4nigd)：272950545
