(function(){"use strict";var rt=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof{}<"u"?{}:typeof self<"u"?self:{},V={},st={get exports(){return V},set exports(i){V=i}};(function(i,r){(function(c,l){i.exports=l()})(rt,function(){var c=1e3,l=6e4,h=36e5,S="millisecond",p="second",v="minute",D="hour",M="day",A="week",$="month",H="quarter",O="year",T="date",w="Invalid Date",mt=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,pt=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,$t={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(s){var n=["th","st","nd","rd"],t=s%100;return"["+s+(n[(t-20)%10]||n[t]||n[0])+"]"}},B=function(s,n,t){var o=String(s);return!o||o.length>=n?s:""+Array(n+1-o.length).join(t)+s},yt={s:B,z:function(s){var n=-s.utcOffset(),t=Math.abs(n),o=Math.floor(t/60),e=t%60;return(n<=0?"+":"-")+B(o,2,"0")+":"+B(e,2,"0")},m:function s(n,t){if(n.date()<t.date())return-s(t,n);var o=12*(t.year()-n.year())+(t.month()-n.month()),e=n.clone().add(o,$),u=t-e<0,a=n.clone().add(o+(u?-1:1),$);return+(-(o+(t-e)/(u?e-a:a-e))||0)},a:function(s){return s<0?Math.ceil(s)||0:Math.floor(s)},p:function(s){return{M:$,y:O,w:A,d:M,D:T,h:D,m:v,s:p,ms:S,Q:H}[s]||String(s||"").toLowerCase().replace(/s$/,"")},u:function(s){return s===void 0}},W="en",Y={};Y[W]=$t;var R=function(s){return s instanceof z},G=function s(n,t,o){var e;if(!n)return W;if(typeof n=="string"){var u=n.toLowerCase();Y[u]&&(e=u),t&&(Y[u]=t,e=u);var a=n.split("-");if(!e&&a.length>1)return s(a[0])}else{var d=n.name;Y[d]=n,e=d}return!o&&e&&(W=e),e||!o&&W},g=function(s,n){if(R(s))return s.clone();var t=typeof n=="object"?n:{};return t.date=s,t.args=arguments,new z(t)},f=yt;f.l=G,f.i=R,f.w=function(s,n){return g(s,{locale:n.$L,utc:n.$u,x:n.$x,$offset:n.$offset})};var z=function(){function s(t){this.$L=G(t.locale,null,!0),this.parse(t)}var n=s.prototype;return n.parse=function(t){this.$d=function(o){var e=o.date,u=o.utc;if(e===null)return new Date(NaN);if(f.u(e))return new Date;if(e instanceof Date)return new Date(e);if(typeof e=="string"&&!/Z$/i.test(e)){var a=e.match(mt);if(a){var d=a[2]-1||0,y=(a[7]||"0").substring(0,3);return u?new Date(Date.UTC(a[1],d,a[3]||1,a[4]||0,a[5]||0,a[6]||0,y)):new Date(a[1],d,a[3]||1,a[4]||0,a[5]||0,a[6]||0,y)}}return new Date(e)}(t),this.$x=t.x||{},this.init()},n.init=function(){var t=this.$d;this.$y=t.getFullYear(),this.$M=t.getMonth(),this.$D=t.getDate(),this.$W=t.getDay(),this.$H=t.getHours(),this.$m=t.getMinutes(),this.$s=t.getSeconds(),this.$ms=t.getMilliseconds()},n.$utils=function(){return f},n.isValid=function(){return this.$d.toString()!==w},n.isSame=function(t,o){var e=g(t);return this.startOf(o)<=e&&e<=this.endOf(o)},n.isAfter=function(t,o){return g(t)<this.startOf(o)},n.isBefore=function(t,o){return this.endOf(o)<g(t)},n.$g=function(t,o,e){return f.u(t)?this[o]:this.set(e,t)},n.unix=function(){return Math.floor(this.valueOf()/1e3)},n.valueOf=function(){return this.$d.getTime()},n.startOf=function(t,o){var e=this,u=!!f.u(o)||o,a=f.p(t),d=function(L,_){var q=f.w(e.$u?Date.UTC(e.$y,_,L):new Date(e.$y,_,L),e);return u?q:q.endOf(M)},y=function(L,_){return f.w(e.toDate()[L].apply(e.toDate("s"),(u?[0,0,0,0]:[23,59,59,999]).slice(_)),e)},m=this.$W,b=this.$M,C=this.$D,x="set"+(this.$u?"UTC":"");switch(a){case O:return u?d(1,0):d(31,11);case $:return u?d(1,b):d(0,b+1);case A:var j=this.$locale().weekStart||0,P=(m<j?m+7:m)-j;return d(u?C-P:C+(6-P),b);case M:case T:return y(x+"Hours",0);case D:return y(x+"Minutes",1);case v:return y(x+"Seconds",2);case p:return y(x+"Milliseconds",3);default:return this.clone()}},n.endOf=function(t){return this.startOf(t,!1)},n.$set=function(t,o){var e,u=f.p(t),a="set"+(this.$u?"UTC":""),d=(e={},e[M]=a+"Date",e[T]=a+"Date",e[$]=a+"Month",e[O]=a+"FullYear",e[D]=a+"Hours",e[v]=a+"Minutes",e[p]=a+"Seconds",e[S]=a+"Milliseconds",e)[u],y=u===M?this.$D+(o-this.$W):o;if(u===$||u===O){var m=this.clone().set(T,1);m.$d[d](y),m.init(),this.$d=m.set(T,Math.min(this.$D,m.daysInMonth())).$d}else d&&this.$d[d](y);return this.init(),this},n.set=function(t,o){return this.clone().$set(t,o)},n.get=function(t){return this[f.p(t)]()},n.add=function(t,o){var e,u=this;t=Number(t);var a=f.p(o),d=function(b){var C=g(u);return f.w(C.date(C.date()+Math.round(b*t)),u)};if(a===$)return this.set($,this.$M+t);if(a===O)return this.set(O,this.$y+t);if(a===M)return d(1);if(a===A)return d(7);var y=(e={},e[v]=l,e[D]=h,e[p]=c,e)[a]||1,m=this.$d.getTime()+t*y;return f.w(m,this)},n.subtract=function(t,o){return this.add(-1*t,o)},n.format=function(t){var o=this,e=this.$locale();if(!this.isValid())return e.invalidDate||w;var u=t||"YYYY-MM-DDTHH:mm:ssZ",a=f.z(this),d=this.$H,y=this.$m,m=this.$M,b=e.weekdays,C=e.months,x=function(_,q,Q,J){return _&&(_[q]||_(o,u))||Q[q].slice(0,J)},j=function(_){return f.s(d%12||12,_,"0")},P=e.meridiem||function(_,q,Q){var J=_<12?"AM":"PM";return Q?J.toLowerCase():J},L={YY:String(this.$y).slice(-2),YYYY:this.$y,M:m+1,MM:f.s(m+1,2,"0"),MMM:x(e.monthsShort,m,C,3),MMMM:x(C,m),D:this.$D,DD:f.s(this.$D,2,"0"),d:String(this.$W),dd:x(e.weekdaysMin,this.$W,b,2),ddd:x(e.weekdaysShort,this.$W,b,3),dddd:b[this.$W],H:String(d),HH:f.s(d,2,"0"),h:j(1),hh:j(2),a:P(d,y,!0),A:P(d,y,!1),m:String(y),mm:f.s(y,2,"0"),s:String(this.$s),ss:f.s(this.$s,2,"0"),SSS:f.s(this.$ms,3,"0"),Z:a};return u.replace(pt,function(_,q){return q||L[_]||a.replace(":","")})},n.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},n.diff=function(t,o,e){var u,a=f.p(o),d=g(t),y=(d.utcOffset()-this.utcOffset())*l,m=this-d,b=f.m(this,d);return b=(u={},u[O]=b/12,u[$]=b,u[H]=b/3,u[A]=(m-y)/6048e5,u[M]=(m-y)/864e5,u[D]=m/h,u[v]=m/l,u[p]=m/c,u)[a]||m,e?b:f.a(b)},n.daysInMonth=function(){return this.endOf($).$D},n.$locale=function(){return Y[this.$L]},n.locale=function(t,o){if(!t)return this.$L;var e=this.clone(),u=G(t,o,!0);return u&&(e.$L=u),e},n.clone=function(){return f.w(this.$d,this)},n.toDate=function(){return new Date(this.valueOf())},n.toJSON=function(){return this.isValid()?this.toISOString():null},n.toISOString=function(){return this.$d.toISOString()},n.toString=function(){return this.$d.toUTCString()},s}(),it=z.prototype;return g.prototype=it,[["$ms",S],["$s",p],["$m",v],["$H",D],["$W",M],["$M",$],["$y",O],["$D",T]].forEach(function(s){it[s[1]]=function(n){return this.$g(n,s[0],s[1])}}),g.extend=function(s,n){return s.$i||(s(n,z,g),s.$i=!0),g},g.locale=G,g.isDayjs=R,g.unix=function(s){return g(1e3*s)},g.en=Y[W],g.Ls=Y,g.p={},g})})(st);const ot=V;function at(i="YYYY_MM_DD_HH_mm_ss"){return ot(new Date).format(i)}function ut(i,r){const c=document.createElement("a");c.setAttribute("href",i),c.setAttribute("download",r),c.click()}const K=async(i,r,c)=>new Promise(l=>{const h=new MutationObserver(()=>{!!i.querySelector(r)==c&&(h.disconnect(),l(void 0))});h.observe(i,{childList:!0,subtree:!0}),!!i.querySelector(r)==c&&l(void 0)}),X=i=>new Promise(function(r,c){setTimeout(()=>c("Timeout"),i)}),tt=(i,r)=>Promise.race([K(i,r,!0),X(1e4)]),ct=(i,r)=>Promise.race([K(i,r,!1),X(1e4)]),E=async(i,r,c)=>{var v;const l=await(await fetch(r)).blob(),h=new File([l],c),S=new DataTransfer;S.items.add(h),(v=i.querySelector("button[aria-label='Clear']"))==null||v.click(),await ct(i,"button[aria-label='Clear']");const p=i.querySelector("input[type='file']");p.value="",p.files=S.files,p.dispatchEvent(new Event("change",{bubbles:!0,composed:!0})),await tt(i,"button[aria-label='Clear']")},I=(i,r)=>{i.querySelectorAll("button")[r].click()},dt=i=>{const r=i.querySelector(":scope > .label-wrap");r&&(r.classList.contains("open")||r.click())},ft=i=>{gradioApp().querySelector("#openpose3d_iframe").contentWindow.postMessage(i,"*")},k={},N={},lt=()=>{window.addEventListener("message",i=>{var c,l;const{data:r}=i;if(r&&r.cmd&&r.cmd=="openpose-3d"&&r.method){const h=r.method;console.log("Method",h,i),r.type=="return"?(c=k[h])==null||c.call(k,r.payload):r.type=="event"&&(console.log(N),(l=N[h])==null||l.call(N,r.payload))}})},ht=i=>{Object.assign(N,i)};function U(i,...r){return new Promise((c,l)=>{const h=setTimeout(()=>{delete k[i],l({method:i,status:"Timeout"})},1e3),S=p=>{clearTimeout(h),c(p)};k[i]=S,ft({cmd:"openpose-3d",method:i,type:"call",payload:r})})}const et=()=>{const i=gradioApp().querySelector("#tab_threedopenpose");return i&&i.style.display!="none"},nt=async(i,r,c,l,h,S,p,v,D)=>{var H,O,T;let M=i.querySelector("#controlnet");if(M)dt(M);else{for(const w of i.querySelectorAll(".cursor-pointer > span"))(H=w.textContent)!=null&&H.includes("ControlNet")&&((O=w.textContent)!=null&&O.includes("M2M")||(M=(T=w.parentElement)==null?void 0:T.parentElement));if(!M){console.error("ControlNet element not found");return}}await tt(M,'div[data-testid="image"]');const A=M.querySelectorAll('div[data-testid="image"]'),$=M.querySelector(".tab-nav");if(r&&c!=""&&c!="-"){const w=Number(c);$&&I($,w),await E(A[w],r,"pose.png")}if(l&&h!=""&&h!="-"){const w=Number(h);$&&I($,w),await E(A[w],l,"depth.png")}if(S&&p!=""&&p!="-"){const w=Number(p);$&&I($,w),await E(A[w],S,"normal.png")}if(v&&D!=""&&D!="-"){const w=Number(D);$&&I($,w),await E(A[w],v,"canny.png")}};let Z=!1,F=!1;onUiLoaded(async()=>{console.log("sd-webui-3d-open-pose-editor: onUiLoaded"),window.openpose3d={sendTxt2img:async(i,r,c,l,h,S,p,v)=>{const D=gradioApp().querySelector("#txt2img_script_container");switch_to_txt2img(),await nt(D,i,r,c,l,h,S,p,v)},sendImg2img:async(i,r,c,l,h,S,p,v)=>{const D=gradioApp().querySelector("#img2img_script_container");switch_to_img2img(),await nt(D,i,r,c,l,h,S,p,v)},downloadImage:(i,r)=>{if(!i)return;const c=r+"_"+at()+".png";ut(i,c)}},lt(),ht({MakeImages:async i=>{for(const[c,l]of Object.entries(i)){const h=gradioApp().querySelector(`#openpose3d_${c}_image`);await E(h,l,c+".png")}const r=gradioApp().querySelector("#openpose3d_main");I(r,1)}});for(let i=0;i<30;++i)try{await U("GetAppVersion"),Z=!0;break}catch(r){if(r.status!="Timeout")throw r}if(!Z){console.error("sd-webui-3d-open-pose-editor: Timeout");return}et()||await U("Pause")}),onUiUpdate(async()=>{Z&&(et()?F&&(F=!1,await U("Resume")):F||(F=!0,await U("Pause")))})})();
